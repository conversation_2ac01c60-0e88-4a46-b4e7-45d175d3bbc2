import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import Home from '../views/Home.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home,
      redirect: '/dashboard',
      meta: { title: '首页', requiresAuth: true },
      children: [
        {
          path: '/dashboard',
          name: 'dashboard',
          meta: { title: '控制台', icon: 'dashboard' },
          component: () => import('../views/Dashboard.vue'),
        },
        {
          path: '/goods',
          name: 'goods',
          meta: { title: '商品', icon: 'goods' },
          component: () => import('../views/goods/Index.vue'),
          redirect: '/goods/list',
          children: [
            {
              path: '/goods/list',
              name: 'goodsList',
              meta: { title: '商品列表', icon: '' },
              component: () => import('../views/goods/List.vue'),
            },
            {
              path: '/goods/category',
              name: 'goodsCategory',
              meta: { title: '商品分类', icon: '' },
              component: () => import('../views/goods/Category.vue'),
            },
            {
              path: '/goods/comment',
              name: 'goodsComment',
              meta: { title: '商品评价', icon: '' },
              component: () => import('../views/goods/Comment.vue'),
            },
            {
              path: '/goods/service',
              name: 'goodsService',
              meta: { title: '商品服务', icon: '' },
              component: () => import('../views/goods/Service.vue'),
            },
          ],
        },
        {
          path: '/promotion',
          name: 'promotion',
          meta: { title: '营销', icon: 'gift' },
          component: () => import('../views/promotion/Index.vue'),
        },
        {
          path: '/order',
          name: 'order',
          meta: { title: '订单', icon: 'order' },
          component: () => import('../views/order/Index.vue'),
        },
        {
          path: '/design',
          name: 'design',
          meta: { title: '装修', icon: 'painter' },
          component: () => import('../views/design/Index.vue'),
        },
        {
          path: '/dealer',
          name: 'dealer',
          meta: { title: '分销', icon: 'share' },
          component: () => import('../views/dealer/Index.vue'),
        },
        {
          path: '/article',
          name: 'article',
          meta: { title: '内容', icon: 'article' },
          component: () => import('../views/article/Index.vue'),
        },
        {
          path: '/finance',
          name: 'finance',
          meta: { title: '财务', icon: 'finance' },
          component: () => import('../views/finance/Index.vue'),
        },
        {
          path: '/user',
          name: 'user',
          meta: { title: '会员', icon: 'user' },
          component: () => import('../views/user/Index.vue'),
        },
        {
          path: '/role',
          name: 'role',
          meta: { title: '权限', icon: 'safe' },
          component: () => import('../views/role/Index.vue'),
        },
        {
          path: '/config',
          name: 'config',
          meta: { title: '设置', icon: 'gear' },
          component: () => import('../views/config/Index.vue'),
        },
      ],
    },
    {
      path: '/login',
      name: 'login',
      meta: { title: '登录' },
      component: () => import('../views/Login.vue'),
    },
  ],
})
// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 检查路由是否需要认证
  if (to.meta.requiresAuth) {
    if (!userStore.isAuthenticated) {
      // 未认证，跳转到登录页，并记录目标路由
      next({
        path: '/login',
        query: { redirect: to.fullPath },
      })
      return
    }
  }

  // 如果已认证且访问登录页，重定向到首页
  if (to.name === 'Login' && userStore.isAuthenticated) {
    next({ path: '/' })
    return
  }

  next()
})

export default router

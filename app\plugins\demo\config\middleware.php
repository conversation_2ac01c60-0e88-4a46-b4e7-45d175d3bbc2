<?php
// demo 插件中间件配置示例
return [
    // 全局中间件（会被添加到应用的全局中间件列表中）
    'global' => [
        // 'plugins\demo\middleware\GlobalLog',
    ],
    
    // 中间件别名（方便在路由中使用）
    'alias' => [
        'demo_auth' => 'plugins\demo\middleware\Auth',
        'demo_log' => 'plugins\demo\middleware\Log',
        'demo_api_auth' => 'plugins\demo\middleware\ApiAuth',
        'demo_upload' => 'plugins\demo\middleware\Upload',
        'demo_cors' => 'plugins\demo\middleware\Cors',
    ],
    
    // 中间件分组
    'group' => [
        'demo_admin' => [
            'plugins\demo\middleware\Auth',
            'plugins\demo\middleware\AdminAuth',
            'plugins\demo\middleware\Log'
        ],
        'demo_api' => [
            'plugins\demo\middleware\ApiAuth',
            'plugins\demo\middleware\Cors',
            'plugins\demo\middleware\RateLimit'
        ]
    ],
    
    // 路由中间件（基于路由模式自动应用）
    'route' => [
        // 所有 demo/admin 开头的路由都会应用这些中间件
        'demo/admin/*' => ['demo_auth', 'demo_log'],
        
        // 所有 demo/api 开头的路由都会应用这些中间件
        'demo/api/*' => ['demo_api_auth', 'demo_cors'],
        
        // 特定路由的中间件
        'demo/upload' => ['demo_upload'],
        'demo/sensitive/*' => ['demo_auth', 'demo_log', 'demo_sensitive'],
        
        // 支持更复杂的模式匹配
        'demo/user/*/edit' => ['demo_auth', 'demo_owner_check'],
        'demo/*/admin' => ['demo_auth', 'demo_admin_check']
    ]
];

@import 'ant-design-vue/dist/reset.css';
#app {
  height: 100%;
  .ant-app,
  .ant-layout {
    height: 100%;
  }
}
.ant-page-header {
  border: none;
}
.panel {
  padding: 12px 24px 24px;
  background-color: #ffffff;
  .panel-head {
    color: rgba(0, 0, 0, 0.88);
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 12px;
  }
}

.ant-table {
  .ant-table-thead {
    > tr > th {
      background: #f5f7fa;
      border-bottom: none;
    }
  }
  .ant-table-container table > thead {
    > tr:first-child > *:last-child {
      border-start-end-radius: 6px;
      border-end-end-radius: 6px;
    }
    > tr:first-child > *:first-child {
      border-start-start-radius: 6px;
      border-end-start-radius: 6px;
    }
  }
}

/* 定义滚动条的宽度和背景颜色 */
::-webkit-scrollbar {
  width: 6px;
  background-color: #f5f5f5;
}

/* 定义滚动条滑块的样式 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #dfdfdf;
}

# 插件路由和中间件配置指南

本文档介绍如何在插件中使用两种方式来注册路由和中间件：传统方式和配置方式。

## 功能特性

- ✅ 支持传统的 `route.php` 文件（直接使用 Route facade）
- ✅ 支持配置式的 `config/route.php` 文件（返回数组配置）
- ✅ 支持多种 HTTP 方法（GET、POST、PUT、DELETE、PATCH、ANY、Resource）
- ✅ 支持路由选项配置（中间件、缓存、验证等）
- ✅ 支持中间件别名和分组
- ✅ 支持基于路由模式的自动中间件应用
- ✅ 支持全局中间件注册
- ✅ 自动为配置式路由添加插件前缀

## 路由注册方式

### 方式一：传统路由文件（推荐）

在插件根目录创建 `route.php` 文件，直接使用 ThinkPHP 的 Route facade：

```
plugins/
└── YourPlugin/
    └── route.php
```

**示例：**

```php
<?php
use think\facade\Route;

// 插件路由定义
Route::group('yourplugin', function () {
    // 基本路由
    Route::get('index', 'plugins\YourPlugin\controller\Index/index');
    Route::post('save', 'plugins\YourPlugin\controller\Index/save');

    // 带中间件的路由
    Route::get('admin', 'plugins\YourPlugin\controller\Admin/index')
        ->middleware(['auth', 'admin']);

    // 资源路由
    Route::resource('posts', 'plugins\YourPlugin\controller\Post');

    // API 分组
    Route::group('api', function () {
        Route::get('data', 'plugins\YourPlugin\controller\Api/getData');
        Route::post('upload', 'plugins\YourPlugin\controller\Api/upload');
    })->middleware('api_auth');

})->allowCrossDomain();

// 独立路由
Route::get('yourplugin/public', 'plugins\YourPlugin\controller\Public/index');
```

### 方式二：配置式路由文件

在插件目录下创建 `config/route.php` 文件，返回路由配置数组：

```
plugins/
└── YourPlugin/
    └── config/
        └── route.php
```

## 配置式路由

### 基本配置格式

```php
<?php
// plugins/YourPlugin/config/route.php
return [
    [
        'method' => 'get',
        'rule' => 'index',  // 会自动添加插件前缀，实际路由为 yourplugin/index
        'route' => 'plugins\YourPlugin\controller\Index/index',
        'option' => [
            'middleware' => ['auth'],
            'cache' => 3600
        ]
    ]
];
```

**重要说明：**

- 配置式路由会自动为 `rule` 添加插件前缀（如 `yourplugin/`）
- 如果 `rule` 已经包含插件前缀，则不会重复添加
- 两种方式可以同时使用，都会生效

### 支持的路由方法

- `get` - GET 请求
- `post` - POST 请求
- `put` - PUT 请求
- `delete` - DELETE 请求
- `patch` - PATCH 请求
- `any` - 任意请求方法
- `resource` - 资源路由
- `get|post` - 多种方法组合

### 路由选项配置

```php
'option' => [
    'middleware' => ['auth', 'log'],           // 中间件
    'domain' => 'api',                         // 域名绑定
    'ext' => 'html',                          // URL后缀
    'https' => true,                          // 强制HTTPS
    'complete_match' => false,                // 完全匹配
    'cache' => 3600,                          // 缓存时间
    'ajax' => true,                           // 仅AJAX请求
    'pjax' => false,                          // PJAX请求
    'json' => true,                           // JSON响应
    'validate' => [                           // 参数验证
        'name' => 'require|max:50',
        'email' => 'email'
    ],
    'filter' => ['strip_tags'],               // 参数过滤
    'append' => ['user_id' => 1],             // 追加参数
    'model' => 'User',                        // 模型绑定
    'auto_search' => true,                    // 自动搜索
    'allow_cross_domain' => true,             // 允许跨域
    'deny_cross_domain' => false,             // 禁止跨域
    'header' => ['Content-Type' => 'application/json'], // 响应头
    'pattern' => [                            // 变量规则
        'id' => '\d+',
        'name' => '\w+'
    ]
]
```

## 中间件配置

### 配置文件位置

在插件目录下创建 `config/middleware.php` 文件：

```
plugins/
└── YourPlugin/
    └── config/
        └── middleware.php
```

### 中间件配置示例

```php
<?php
// plugins/YourPlugin/config/middleware.php
return [
    // 全局中间件
    'global' => [
        'plugins\YourPlugin\middleware\GlobalLog',
    ],

    // 中间件别名
    'alias' => [
        'plugin_auth' => 'plugins\YourPlugin\middleware\Auth',
        'plugin_log' => 'plugins\YourPlugin\middleware\Log',
    ],

    // 中间件分组
    'group' => [
        'plugin_admin' => [
            'plugins\YourPlugin\middleware\Auth',
            'plugins\YourPlugin\middleware\AdminAuth'
        ]
    ],

    // 路由中间件（基于模式匹配）
    'route' => [
        'yourplugin/admin/*' => ['plugin_auth', 'plugin_log'],
        'yourplugin/api/*' => ['plugin_auth'],
        'yourplugin/upload' => ['plugin_upload']
    ]
];
```

### 中间件类示例

```php
<?php
namespace plugins\YourPlugin\middleware;

use think\Request;
use think\Response;

class Auth
{
    public function handle(Request $request, \Closure $next)
    {
        // 认证逻辑
        $token = $request->header('Authorization');

        if (!$token || !$this->validateToken($token)) {
            return json(['error' => 'Unauthorized'], 401);
        }

        return $next($request);
    }

    protected function validateToken($token)
    {
        // 验证token逻辑
        return !empty($token);
    }
}
```

## 路由模式匹配

路由中间件支持通配符模式匹配：

- `yourplugin/admin/*` - 匹配所有以 `yourplugin/admin/` 开头的路由
- `yourplugin/*/edit` - 匹配如 `yourplugin/user/edit`、`yourplugin/post/edit` 等
- `yourplugin/user/:id` - 匹配带参数的路由

## 实际使用示例

### 示例：博客插件的路由配置

**传统方式 - plugins/Blog/route.php：**

```php
<?php
use think\facade\Route;

Route::group('blog', function () {
    // 前台路由
    Route::get('', 'plugins\Blog\controller\Index/index');
    Route::get('post/:id', 'plugins\Blog\controller\Post/show')
        ->pattern(['id' => '\d+']);
    Route::get('category/:slug', 'plugins\Blog\controller\Category/show');

    // 管理后台
    Route::group('admin', function () {
        Route::resource('posts', 'plugins\Blog\controller\admin\Post');
        Route::resource('categories', 'plugins\Blog\controller\admin\Category');
        Route::get('dashboard', 'plugins\Blog\controller\admin\Dashboard/index');
    })->middleware(['blog_auth', 'blog_admin']);

    // API 接口
    Route::group('api', function () {
        Route::get('posts', 'plugins\Blog\controller\api\Post/index');
        Route::get('posts/:id', 'plugins\Blog\controller\api\Post/show');
        Route::post('posts', 'plugins\Blog\controller\api\Post/create');
    })->middleware('blog_api_auth');

})->allowCrossDomain();
```

**配置方式 - plugins/Blog/config/route.php：**

```php
<?php
return [
    // 额外的配置式路由（与传统路由并存）
    [
        'method' => 'get',
        'rule' => 'rss',  // 实际路由：blog/rss
        'route' => 'plugins\Blog\controller\Rss/index',
        'option' => [
            'cache' => 3600,
            'ext' => 'xml'
        ]
    ],

    [
        'method' => 'post',
        'rule' => 'webhook/comment',  // 实际路由：blog/webhook/comment
        'route' => 'plugins\Blog\controller\Webhook/comment',
        'option' => [
            'middleware' => ['blog_webhook_verify'],
            'validate' => [
                'post_id' => 'require|integer',
                'content' => 'require|max:500'
            ]
        ]
    ]
];
```

### 完整的中间件配置

```php
<?php
// plugins/Blog/config/middleware.php
return [
    'alias' => [
        'blog_auth' => 'plugins\Blog\middleware\Auth',
        'blog_admin_auth' => 'plugins\Blog\middleware\AdminAuth',
        'blog_api_auth' => 'plugins\Blog\middleware\ApiAuth',
        'blog_log' => 'plugins\Blog\middleware\Log'
    ],

    'group' => [
        'blog_admin' => ['blog_auth', 'blog_admin_auth', 'blog_log'],
        'blog_api' => ['blog_api_auth', 'blog_log']
    ],

    'route' => [
        'blog/admin/*' => ['blog_admin_auth', 'blog_log'],
        'blog/api/*' => ['blog_api_auth'],
        'blog/user/*' => ['blog_auth']
    ]
];
```

## 注意事项

1. 路由配置文件必须返回数组格式
2. 中间件类必须实现 `handle` 方法
3. 路由模式匹配区分大小写
4. 全局中间件会影响整个应用，请谨慎使用
5. 中间件执行顺序：全局中间件 → 路由中间件 → 路由选项中间件

## 两种方式对比

### 传统方式 (route.php) 的优势

- ✅ **灵活性高**：可以使用 Route facade 的所有功能
- ✅ **分组方便**：支持嵌套分组和复杂的路由结构
- ✅ **IDE 支持**：更好的代码提示和语法检查
- ✅ **性能更好**：直接注册，无需额外解析
- ✅ **功能完整**：支持所有 ThinkPHP 路由特性

### 配置方式 (config/route.php) 的优势

- ✅ **结构化**：数组格式便于程序化处理
- ✅ **自动前缀**：自动添加插件前缀，避免冲突
- ✅ **易于管理**：可以通过程序动态生成或修改
- ✅ **统一格式**：所有插件使用相同的配置格式

### 推荐使用场景

- **传统方式**：适合复杂的路由结构、需要高度自定义的场景
- **配置方式**：适合简单路由、需要程序化管理的场景
- **混合使用**：复杂路由用传统方式，简单路由用配置方式

## 更新的注意事项

1. 两种方式可以同时使用，都会生效
2. 配置式路由会自动添加插件前缀，避免路由冲突
3. 中间件类必须实现 `handle` 方法
4. 路由模式匹配区分大小写
5. 全局中间件会影响整个应用，请谨慎使用
6. 中间件执行顺序：全局中间件 → 路由中间件 → 路由选项中间件
7. 传统方式的路由会先于配置方式注册

## API 参考

### PluginService 更新的方法

- `registerConfigRoutes($routes, $pluginName)` - 注册配置文件中的路由
- `registerSingleRoute($config, $pluginName)` - 注册单个路由配置
- `loadPluginMiddlewares($pluginName)` - 加载插件中间件
- `getRouteMiddlewares($route, $pluginName = null)` - 获取路由对应的中间件
- `applyRouteOptions($routeObj, $options)` - 应用路由选项

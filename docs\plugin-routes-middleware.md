# 插件路由和中间件配置指南

本文档介绍如何在插件中使用配置文件来动态注册路由和中间件。

## 功能特性

- ✅ 支持通过配置文件动态注册路由
- ✅ 支持多种HTTP方法（GET、POST、PUT、DELETE、PATCH、ANY、Resource）
- ✅ 支持路由选项配置（中间件、缓存、验证等）
- ✅ 支持中间件别名和分组
- ✅ 支持基于路由模式的自动中间件应用
- ✅ 支持全局中间件注册

## 路由配置

### 配置文件位置

在插件目录下创建 `config/route.php` 文件：

```
plugins/
└── YourPlugin/
    └── config/
        └── route.php
```

### 基本路由配置

```php
<?php
// plugins/YourPlugin/config/route.php
return [
    [
        'method' => 'get',
        'rule' => 'yourplugin/index',
        'route' => 'plugins\YourPlugin\controller\Index/index',
        'option' => [
            'middleware' => ['auth'],
            'cache' => 3600
        ]
    ]
];
```

### 支持的路由方法

- `get` - GET请求
- `post` - POST请求  
- `put` - PUT请求
- `delete` - DELETE请求
- `patch` - PATCH请求
- `any` - 任意请求方法
- `resource` - 资源路由
- `get|post` - 多种方法组合

### 路由选项配置

```php
'option' => [
    'middleware' => ['auth', 'log'],           // 中间件
    'domain' => 'api',                         // 域名绑定
    'ext' => 'html',                          // URL后缀
    'https' => true,                          // 强制HTTPS
    'complete_match' => false,                // 完全匹配
    'cache' => 3600,                          // 缓存时间
    'ajax' => true,                           // 仅AJAX请求
    'pjax' => false,                          // PJAX请求
    'json' => true,                           // JSON响应
    'validate' => [                           // 参数验证
        'name' => 'require|max:50',
        'email' => 'email'
    ],
    'filter' => ['strip_tags'],               // 参数过滤
    'append' => ['user_id' => 1],             // 追加参数
    'model' => 'User',                        // 模型绑定
    'auto_search' => true,                    // 自动搜索
    'allow_cross_domain' => true,             // 允许跨域
    'deny_cross_domain' => false,             // 禁止跨域
    'header' => ['Content-Type' => 'application/json'], // 响应头
    'pattern' => [                            // 变量规则
        'id' => '\d+',
        'name' => '\w+'
    ]
]
```

## 中间件配置

### 配置文件位置

在插件目录下创建 `config/middleware.php` 文件：

```
plugins/
└── YourPlugin/
    └── config/
        └── middleware.php
```

### 中间件配置示例

```php
<?php
// plugins/YourPlugin/config/middleware.php
return [
    // 全局中间件
    'global' => [
        'plugins\YourPlugin\middleware\GlobalLog',
    ],
    
    // 中间件别名
    'alias' => [
        'plugin_auth' => 'plugins\YourPlugin\middleware\Auth',
        'plugin_log' => 'plugins\YourPlugin\middleware\Log',
    ],
    
    // 中间件分组
    'group' => [
        'plugin_admin' => [
            'plugins\YourPlugin\middleware\Auth',
            'plugins\YourPlugin\middleware\AdminAuth'
        ]
    ],
    
    // 路由中间件（基于模式匹配）
    'route' => [
        'yourplugin/admin/*' => ['plugin_auth', 'plugin_log'],
        'yourplugin/api/*' => ['plugin_auth'],
        'yourplugin/upload' => ['plugin_upload']
    ]
];
```

### 中间件类示例

```php
<?php
namespace plugins\YourPlugin\middleware;

use think\Request;
use think\Response;

class Auth
{
    public function handle(Request $request, \Closure $next)
    {
        // 认证逻辑
        $token = $request->header('Authorization');
        
        if (!$token || !$this->validateToken($token)) {
            return json(['error' => 'Unauthorized'], 401);
        }
        
        return $next($request);
    }
    
    protected function validateToken($token)
    {
        // 验证token逻辑
        return !empty($token);
    }
}
```

## 路由模式匹配

路由中间件支持通配符模式匹配：

- `yourplugin/admin/*` - 匹配所有以 `yourplugin/admin/` 开头的路由
- `yourplugin/*/edit` - 匹配如 `yourplugin/user/edit`、`yourplugin/post/edit` 等
- `yourplugin/user/:id` - 匹配带参数的路由

## 使用示例

### 完整的插件路由配置

```php
<?php
// plugins/Blog/config/route.php
return [
    // 博客首页
    [
        'method' => 'get',
        'rule' => 'blog',
        'route' => 'plugins\Blog\controller\Index/index'
    ],
    
    // 文章详情
    [
        'method' => 'get', 
        'rule' => 'blog/post/:id',
        'route' => 'plugins\Blog\controller\Post/show',
        'option' => [
            'pattern' => ['id' => '\d+'],
            'cache' => 1800
        ]
    ],
    
    // 管理后台
    [
        'method' => 'resource',
        'rule' => 'blog/admin/posts',
        'route' => 'plugins\Blog\controller\admin\Post',
        'option' => [
            'middleware' => ['blog_admin_auth']
        ]
    ],
    
    // API接口
    [
        'method' => 'get',
        'rule' => 'blog/api/posts',
        'route' => 'plugins\Blog\controller\api\Post/index',
        'option' => [
            'middleware' => ['blog_api_auth'],
            'json' => true,
            'allow_cross_domain' => true
        ]
    ]
];
```

### 完整的中间件配置

```php
<?php
// plugins/Blog/config/middleware.php
return [
    'alias' => [
        'blog_auth' => 'plugins\Blog\middleware\Auth',
        'blog_admin_auth' => 'plugins\Blog\middleware\AdminAuth',
        'blog_api_auth' => 'plugins\Blog\middleware\ApiAuth',
        'blog_log' => 'plugins\Blog\middleware\Log'
    ],
    
    'group' => [
        'blog_admin' => ['blog_auth', 'blog_admin_auth', 'blog_log'],
        'blog_api' => ['blog_api_auth', 'blog_log']
    ],
    
    'route' => [
        'blog/admin/*' => ['blog_admin_auth', 'blog_log'],
        'blog/api/*' => ['blog_api_auth'],
        'blog/user/*' => ['blog_auth']
    ]
];
```

## 注意事项

1. 路由配置文件必须返回数组格式
2. 中间件类必须实现 `handle` 方法
3. 路由模式匹配区分大小写
4. 全局中间件会影响整个应用，请谨慎使用
5. 中间件执行顺序：全局中间件 → 路由中间件 → 路由选项中间件

## API参考

### PluginService 新增方法

- `registerConfigRoutes($routes)` - 注册配置文件中的路由
- `loadPluginMiddlewares($pluginName)` - 加载插件中间件
- `getRouteMiddlewares($route, $pluginName = null)` - 获取路由对应的中间件

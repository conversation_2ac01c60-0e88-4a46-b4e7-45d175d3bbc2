<?php
/**
 * 插件路由和中间件功能测试脚本
 * 
 * 使用方法：
 * php test_plugin_routes.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\service\PluginService;
use think\App;

echo "=== 插件路由和中间件功能测试 ===\n\n";

// 创建应用实例
$app = new App();
$pluginService = new PluginService($app);

// 测试路由模式匹配
echo "1. 测试路由模式匹配:\n";
$reflection = new ReflectionClass($pluginService);
$matchMethod = $reflection->getMethod('matchRoutePattern');
$matchMethod->setAccessible(true);

$testCases = [
    ['demo/admin/users', 'demo/admin/*', true],
    ['demo/admin/posts/edit', 'demo/admin/*', true],
    ['demo/user/profile', 'demo/admin/*', false],
    ['demo/upload', 'demo/upload', true],
    ['demo/upload/file', 'demo/upload', false],
    ['demo/user/123/edit', 'demo/*/edit', true],
    ['demo/post/456/edit', 'demo/*/edit', true],
    ['demo/user/123/view', 'demo/*/edit', false],
];

foreach ($testCases as $i => $case) {
    list($route, $pattern, $expected) = $case;
    $result = $matchMethod->invoke($pluginService, $route, $pattern);
    $status = $result === $expected ? '✅' : '❌';
    echo "  {$status} 测试 " . ($i + 1) . ": '{$route}' 匹配 '{$pattern}' = " . ($result ? 'true' : 'false') . "\n";
}

echo "\n2. 测试路由配置注册:\n";

// 模拟路由配置
$routes = [
    [
        'method' => 'get',
        'rule' => 'test-index',
        'route' => 'plugins\test\controller\Index/index',
        'option' => [
            'middleware' => ['test_auth'],
            'cache' => 3600
        ]
    ],
    [
        'method' => 'post',
        'rule' => 'test/save',  // 已包含插件前缀
        'route' => 'plugins\test\controller\Index/save',
        'option' => [
            'ajax' => true,
            'validate' => [
                'name' => 'require'
            ]
        ]
    ]
];

try {
    $registerMethod = $reflection->getMethod('registerConfigRoutes');
    $registerMethod->setAccessible(true);
    $registerMethod->invoke($pluginService, $routes, 'test');
    echo "  ✅ 路由配置注册成功\n";
} catch (Exception $e) {
    echo "  ❌ 路由配置注册失败: " . $e->getMessage() . "\n";
}

echo "\n3. 测试中间件配置:\n";

// 模拟中间件配置
$middlewares = [
    'alias' => [
        'test_auth' => 'plugins\test\middleware\Auth',
        'test_log' => 'plugins\test\middleware\Log'
    ],
    'group' => [
        'test_admin' => ['test_auth', 'test_log']
    ],
    'route' => [
        'test/admin/*' => ['test_auth', 'test_log'],
        'test/api/*' => ['test_auth']
    ]
];

try {
    $middlewareMethod = $reflection->getMethod('registerPluginMiddlewares');
    $middlewareMethod->setAccessible(true);
    $middlewareMethod->invoke($pluginService, $middlewares, 'test');
    echo "  ✅ 中间件配置注册成功\n";
} catch (Exception $e) {
    echo "  ❌ 中间件配置注册失败: " . $e->getMessage() . "\n";
}

echo "\n4. 测试获取路由中间件:\n";

$testRoutes = [
    'test/admin/users',
    'test/api/data',
    'test/public/info'
];

foreach ($testRoutes as $route) {
    try {
        $middlewares = $pluginService->getRouteMiddlewares($route, 'test');
        echo "  ✅ 路由 '{$route}' 的中间件: " . (empty($middlewares) ? '无' : implode(', ', $middlewares)) . "\n";
    } catch (Exception $e) {
        echo "  ❌ 获取路由 '{$route}' 中间件失败: " . $e->getMessage() . "\n";
    }
}

echo "\n5. 测试插件前缀自动添加:\n";

$testRules = [
    ['index', 'demo', 'demo/index'],
    ['api/data', 'demo', 'demo/api/data'],
    ['demo/admin', 'demo', 'demo/admin'],  // 已包含前缀，不重复添加
    ['/absolute', 'demo', 'demo/absolute'],  // 去除开头的斜杠
];

foreach ($testRules as $i => $case) {
    list($rule, $pluginName, $expected) = $case;
    
    // 模拟前缀添加逻辑
    if ($pluginName && strpos($rule, $pluginName . '/') !== 0) {
        $result = $pluginName . '/' . ltrim($rule, '/');
    } else {
        $result = $rule;
    }
    
    $status = $result === $expected ? '✅' : '❌';
    echo "  {$status} 测试 " . ($i + 1) . ": '{$rule}' + '{$pluginName}' = '{$result}'\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n使用说明:\n";
echo "1. 传统路由文件: plugins/YourPlugin/route.php\n";
echo "2. 配置式路由文件: plugins/YourPlugin/config/route.php\n";
echo "3. 中间件配置文件: plugins/YourPlugin/config/middleware.php\n";
echo "4. 两种方式可以同时使用\n";
echo "5. 配置式路由会自动添加插件前缀\n";

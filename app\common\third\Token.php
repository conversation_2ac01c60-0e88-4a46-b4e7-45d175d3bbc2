<?php

namespace app\common\third;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class Token
{
    private static $key = 'your_secret_key';

    //生成TOKEN
    public static function create($userId)
    {
        $payload = [
            'iss' => "issuer", //签发者
            'iat' => time(), //签发时间
            'exp' => time() + 7200, //过期时间(2小时)
            'data' => ["user_id" => $userId],
        ];
        return JWT::encode($payload, self::$key, 'HS256');
    }

    //验证TOKEN
    public static function check($token)
    {
        try {
            $decoded = JWT::decode($token, new Key(self::$key, 'HS256'));
            return (array)$decoded->data;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}

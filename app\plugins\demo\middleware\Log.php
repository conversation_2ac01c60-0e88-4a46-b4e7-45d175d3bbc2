<?php

namespace plugins\demo\middleware;

use think\Request;
use think\Response;
use think\facade\Log as ThinkLog;

/**
 * Demo 插件日志中间件
 */
class Log
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next)
    {
        $startTime = microtime(true);
        
        // 记录请求开始
        $this->logRequest($request);
        
        // 执行请求
        $response = $next($request);
        
        // 记录请求结束
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        $this->logResponse($request, $response, $duration);
        
        return $response;
    }
    
    /**
     * 记录请求信息
     */
    protected function logRequest(Request $request)
    {
        $logData = [
            'method' => $request->method(),
            'url' => $request->url(true),
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'params' => $request->param(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        ThinkLog::info('Demo Plugin Request', $logData);
    }
    
    /**
     * 记录响应信息
     */
    protected function logResponse(Request $request, Response $response, $duration)
    {
        $logData = [
            'method' => $request->method(),
            'url' => $request->url(true),
            'status_code' => $response->getCode(),
            'duration' => $duration . 'ms',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        ThinkLog::info('Demo Plugin Response', $logData);
    }
}

<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\model\Admin;
use app\common\third\Token;
use think\exception\ValidateException;
use think\facade\Cache;

/**
 * 后台登录控制器
 */
class Login extends Admin
{
    /**
     * 登录页面
     */
    public function index()
    {
        return json([
            'code' => 0,
            'message' => '请使用POST方法提交登录信息',
            'data' => [
                'login_url' => '/admin/login/check',
                'required_fields' => ['username', 'password']
            ]
        ]);
    }

    /**
     * 处理登录逻辑
     */
    public function check()
    {
        try {
            // 获取请求参数
            $username = $this->request->param('username', '');
            $password = $this->request->param('password', '');
            $remember = $this->request->param('remember', false);

            // 验证参数
            $this->validateLoginData([
                'username' => $username,
                'password' => $password
            ]);

            // 查找用户
            $adminModel = new \app\admin\model\Admin();
            $user = $adminModel->where('username', $username)
                              ->where('deleted_at', null)
                              ->find();

            if (!$user) {
                return $this->error('用户名或密码错误');
            }

            // 验证密码
            if (!$this->verifyPassword($password, $user->password)) {
                // 记录登录失败次数
                $this->recordLoginFailure($username);
                return $this->error('用户名或密码错误');
            }

            // 检查账户状态
            if (!$this->checkAccountStatus($user)) {
                return $this->error('账户已被禁用');
            }

            // 生成token
            $token = Token::create($user->id);

            // 缓存用户信息
            $this->cacheUserInfo($user, $token, $remember);

            // 更新登录信息
            $this->updateLoginInfo($user);

            // 清除登录失败记录
            $this->clearLoginFailure($username);

            return $this->success([
                'token' => $token,
                'user_info' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'full_name' => $user->full_name,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'is_admin' => $user->is_admin,
                    'store_id' => $user->store_id
                ],
                'expires_in' => $remember ? 7200 * 24 : 7200 // 记住登录状态24小时，否则2小时
            ], '登录成功');

        } catch (ValidateException $e) {
            return $this->error($e->getError());
        } catch (\Exception $e) {
            return $this->error('登录失败：' . $e->getMessage());
        }
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        try {
            $token = $this->request->header('Authorization');
            if ($token) {
                // 从缓存中移除token
                Cache::delete('admin_token_' . md5($token));
                
                // 解析token获取用户ID
                $tokenData = Token::check($token);
                if (isset($tokenData['user_id'])) {
                    Cache::delete('admin_user_' . $tokenData['user_id']);
                }
            }

            return $this->success([], '退出成功');
        } catch (\Exception $e) {
            return $this->success([], '退出成功'); // 即使出错也返回成功
        }
    }

    /**
     * 验证登录数据
     */
    protected function validateLoginData($data)
    {
        $rules = [
            'username' => 'require|length:3,50',
            'password' => 'require|length:6,32'
        ];

        $messages = [
            'username.require' => '用户名不能为空',
            'username.length' => '用户名长度必须在3-50个字符之间',
            'password.require' => '密码不能为空',
            'password.length' => '密码长度必须在6-32个字符之间'
        ];

        $this->validate($data, $rules, $messages);
    }

    /**
     * 验证密码
     */
    protected function verifyPassword($inputPassword, $hashedPassword)
    {
        // 如果数据库中的密码是明文（开发环境），直接比较
        if (strlen($hashedPassword) < 60) {
            return $inputPassword === $hashedPassword;
        }
        
        // 使用password_verify验证加密密码
        return password_verify($inputPassword, $hashedPassword);
    }

    /**
     * 检查账户状态
     */
    protected function checkAccountStatus($user)
    {
        // 这里可以添加更多的账户状态检查
        // 比如是否被禁用、是否过期等
        return true;
    }

    /**
     * 缓存用户信息
     */
    protected function cacheUserInfo($user, $token, $remember = false)
    {
        $expireTime = $remember ? 7200 * 24 : 7200; // 记住登录24小时，否则2小时
        
        // 缓存token对应的用户ID
        Cache::set('admin_token_' . md5($token), $user->id, $expireTime);
        
        // 缓存用户信息
        Cache::set('admin_user_' . $user->id, [
            'id' => $user->id,
            'username' => $user->username,
            'full_name' => $user->full_name,
            'email' => $user->email,
            'phone' => $user->phone,
            'is_admin' => $user->is_admin,
            'store_id' => $user->store_id,
            'login_time' => time()
        ], $expireTime);
    }

    /**
     * 更新登录信息
     */
    protected function updateLoginInfo($user)
    {
        $user->save([
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        // 可以在这里记录登录日志
        $this->recordLoginLog($user);
    }

    /**
     * 记录登录日志
     */
    protected function recordLoginLog($user)
    {
        // 这里可以记录登录日志到数据库或日志文件
        // 暂时使用缓存记录最近的登录记录
        $loginLogs = Cache::get('admin_login_logs_' . $user->id, []);
        
        $loginLogs[] = [
            'login_time' => date('Y-m-d H:i:s'),
            'ip' => $this->request->ip(),
            'user_agent' => $this->request->header('User-Agent')
        ];
        
        // 只保留最近10次登录记录
        if (count($loginLogs) > 10) {
            $loginLogs = array_slice($loginLogs, -10);
        }
        
        Cache::set('admin_login_logs_' . $user->id, $loginLogs, 7200 * 24 * 30); // 保存30天
    }

    /**
     * 记录登录失败次数
     */
    protected function recordLoginFailure($username)
    {
        $key = 'admin_login_failure_' . md5($username);
        $failures = Cache::get($key, 0);
        Cache::set($key, $failures + 1, 1800); // 30分钟内有效
    }

    /**
     * 清除登录失败记录
     */
    protected function clearLoginFailure($username)
    {
        $key = 'admin_login_failure_' . md5($username);
        Cache::delete($key);
    }

    /**
     * 检查登录失败次数
     */
    protected function checkLoginFailures($username)
    {
        $key = 'admin_login_failure_' . md5($username);
        $failures = Cache::get($key, 0);
        
        if ($failures >= 5) {
            throw new \Exception('登录失败次数过多，请30分钟后再试');
        }
        
        return $failures;
    }

    /**
     * 获取当前登录用户信息
     */
    public function userInfo()
    {
        try {
            $token = $this->request->header('Authorization');
            if (!$token) {
                return $this->error('未提供认证token');
            }

            $tokenData = Token::check($token);
            if (!isset($tokenData['user_id'])) {
                return $this->error('无效的token');
            }

            $userInfo = Cache::get('admin_user_' . $tokenData['user_id']);
            if (!$userInfo) {
                return $this->error('用户信息已过期，请重新登录');
            }

            return $this->success($userInfo, '获取用户信息成功');
        } catch (\Exception $e) {
            return $this->error('获取用户信息失败：' . $e->getMessage());
        }
    }
}

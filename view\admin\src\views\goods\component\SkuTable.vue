<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import Upload from '@/components/editor/Upload.vue'

const props = defineProps({
  specGroups: {
    type: Array,
    default: () => [],
  },
  specsType: {
    type: String,
    default: 'single',
  },
})

const emit = defineEmits(['update:skuList'])

// SKU数据
const skuList = ref([])

// 上传状态管理
const uploadStates = ref({})

// 处理图片选择
const handleImageSelect = (skuId, selectedImages) => {
  const sku = skuList.value.find((item) => item.id === skuId)
  if (sku && selectedImages && selectedImages.length > 0) {
    // 只取第一张图片作为SKU预览图（限制单张图片）
    const firstImage = selectedImages[0]
    let imageUrl = null

    if (typeof firstImage === 'string') {
      imageUrl = firstImage
    } else if (typeof firstImage === 'number') {
      imageUrl = `https://picsum.photos/60/60?random=${firstImage + 1}`
    } else if (firstImage && typeof firstImage === 'object') {
      imageUrl = firstImage.url || firstImage.src || firstImage.href || firstImage.link
    }

    if (imageUrl) {
      sku.image = imageUrl
      // 更新fileList用于Upload组件显示
      sku.imageList = [
        {
          uid: Date.now(),
          name: 'sku-image.jpg',
          status: 'done',
          url: imageUrl,
          thumbUrl: imageUrl,
        },
      ]
    }
  }
}

// 处理图片删除
const handleImageRemove = (skuId) => {
  const sku = skuList.value.find((item) => item.id === skuId)
  if (sku) {
    // 清空图片相关数据
    sku.image = ''
    sku.imageList = []
  }
}

// 生成SKU组合和表格列
const generateSkuData = () => {
  if (props.specsType === 'single') {
    skuList.value = [
      {
        id: 1,
        key: 1,
        image: '',
        imageList: [],
        price: '',
        line_price: '',
        stock: '',
        weight: '',
        sku_code: '',
      },
    ]
    emit('update:skuList', skuList.value)
    return
  }

  const validGroups = props.specGroups.filter(
    (group) => group.name && group.specs.some((spec) => spec.name),
  )

  if (validGroups.length === 0) {
    skuList.value = []
    emit('update:skuList', skuList.value)
    return
  }

  // 生成笛卡尔积
  const combinations = []
  const generate = (index, current) => {
    if (index === validGroups.length) {
      combinations.push([...current])
      return
    }

    const group = validGroups[index]
    const validSpecs = group.specs.filter((spec) => spec.name)

    for (const spec of validSpecs) {
      current.push({
        groupName: group.name,
        specName: spec.name,
        groupId: group.id,
        specId: spec.id,
      })
      generate(index + 1, current)
      current.pop()
    }
  }

  generate(0, [])

  // 生成SKU数据，包含规格信息
  skuList.value = combinations.map((combo, index) => {
    const skuData = {
      id: index + 1,
      key: index + 1,
      image: '',
      imageList: [],
      price: '',
      line_price: '',
      stock: '',
      weight: '',
      sku_code: '',
    }

    // 为每个规格组添加对应的规格值
    combo.forEach((item) => {
      skuData[`spec_${item.groupId}`] = item.specName
    })

    return skuData
  })

  emit('update:skuList', skuList.value)
}

// 计算行合并 - 优化算法
const getRowSpan = (rowIndex, groupIndex) => {
  if (props.specsType === 'single') return 1

  const validGroups = props.specGroups.filter(
    (group) => group.name && group.specs.some((spec) => spec.name),
  )

  if (groupIndex >= validGroups.length) return 1

  // 计算当前规格组的重复周期
  let repeatCycle = 1
  for (let i = groupIndex + 1; i < validGroups.length; i++) {
    const group = validGroups[i]
    const validSpecs = group.specs.filter((spec) => spec.name)
    repeatCycle *= validSpecs.length
  }

  // 检查是否是该周期的第一行
  if (rowIndex % repeatCycle !== 0) {
    return 0 // 合并到上一行
  }

  return repeatCycle
}

// 获取有效的规格组
const validSpecGroups = computed(() => {
  return props.specGroups.filter((group) => group.name && group.specs.some((spec) => spec.name))
})

// 监听规格类型变化
watch(
  () => props.specsType,
  () => {
    generateSkuData()
  },
  { immediate: true },
)

// 监听规格组变化
watch(
  () => props.specGroups,
  () => {
    if (props.specsType === 'multiple') {
      generateSkuData()
    }
  },
  { deep: true },
)

// 监听SKU数据变化，向父组件发送更新
watch(
  skuList,
  () => {
    emit('update:skuList', skuList.value)
  },
  { deep: true },
)
</script>

<template>
  <div class="sku-table">
    <a-table
      :dataSource="skuList"
      :pagination="false"
      bordered
      size="small"
      :scroll="{ x: 'max-content' }"
      class="sku-table"
    >
      <!-- 动态规格列 -->
      <template v-if="specsType === 'multiple'">
        <a-table-column
          v-for="(group, groupIndex) in validSpecGroups"
          :key="`spec_${group.id}`"
          :title="group.name"
          :dataIndex="`spec_${group.id}`"
          :width="80"
          :customRender="
            ({ record, index }) => {
              const value = record[`spec_${group.id}`]
              const rowSpan = getRowSpan(index, groupIndex)
              return {
                children: value,
                props: { rowSpan },
              }
            }
          "
        />
      </template>

      <!-- 预览图列 -->
      <a-table-column title="预览图" dataIndex="image" key="image" :width="60" align="center">
        <template #default="{ record }">
          <!-- Upload组件 - 使用show-button模式 -->
          <Upload
            v-model="uploadStates[record.id]"
            size="small"
            :max-count="1"
            :show-button="true"
            :file-list="record.imageList || []"
            @image-select="(images) => handleImageSelect(record.id, images)"
            @remove-image="() => handleImageRemove(record.id)"
          />
        </template>
      </a-table-column>

      <!-- 商品价格列 -->
      <a-table-column title="商品价格" dataIndex="price" key="price" :width="90">
        <template #default="{ record }">
          <a-input v-model:value="record.price" placeholder="0.00" prefix="￥" size="small" />
        </template>
      </a-table-column>

      <!-- 划线价格列 -->
      <a-table-column title="划线价格" dataIndex="line_price" key="line_price" :width="90">
        <template #default="{ record }">
          <a-input v-model:value="record.line_price" placeholder="0.00" prefix="￥" size="small" />
        </template>
      </a-table-column>

      <!-- 库存数量列 -->
      <a-table-column title="库存数量" dataIndex="stock" key="stock" :width="90">
        <template #default="{ record }">
          <a-input-number
            v-model:value="record.stock"
            :min="0"
            placeholder="0"
            size="small"
            style="width: 100%"
          />
        </template>
      </a-table-column>

      <!-- 重量列 -->
      <a-table-column title="重量(KG)" dataIndex="weight" key="weight" :width="90">
        <template #default="{ record }">
          <a-input-number
            v-model:value="record.weight"
            :min="0"
            :step="0.1"
            placeholder="0.0"
            size="small"
            style="width: 100%"
          />
        </template>
      </a-table-column>

      <!-- SKU编码列 -->
      <a-table-column title="SKU编码" dataIndex="sku_code" key="sku_code" :width="120">
        <template #default="{ record }">
          <a-input v-model:value="record.sku_code" placeholder="SKU编码" size="small" />
        </template>
      </a-table-column>
    </a-table>
  </div>
</template>

<style lang="less" scoped>
.sku-table {
  :deep(.ant-table) {
    table {
      border-top: none;
    }

    .ant-table-thead > tr > th {
      background-color: #f5f7fa;
      font-weight: 500;
      text-align: center;
    }

    .ant-table-tbody > tr > td {
      text-align: center;
      .ant-input-affix-wrapper-sm {
        padding: 0px 6px;
        border-radius: 4px;
      }
      .ant-input-number-sm input.ant-input-number-input,
      .ant-input-sm {
        height: 30px;
        padding: 4px 6px;
      }
    }

    // Upload组件在单图片模式下会自动处理尺寸和间距
    // 无需额外的样式覆盖
  }
}
</style>

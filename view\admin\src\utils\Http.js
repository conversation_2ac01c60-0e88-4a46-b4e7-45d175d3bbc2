import axios from 'axios'
import router from '@/router'
import { useUserStore } from '@/stores/user'

class Http {
  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_APP_BASE_API,
      timeout: 10000,
    })

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const userStore = useUserStore()
        if (userStore.token) {
          config.headers.Authorization = `Bearer ${userStore.token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      },
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        return response
      },
      (error) => {
        const userStore = useUserStore()
        if (error.response?.status === 401) {
          // Token过期或未认证
          userStore.clearAuth()
          // 跳转到登录页，并携带重定向路径
          const redirect = router.currentRoute.value.fullPath
          router.push(`/login?redirect=${encodeURIComponent(redirect)}`)
          return Promise.reject(new Error('认证已过期，请重新登录'))
        }
        // 其他错误处理
        let message = ''
        if (error.response) {
          switch (error.response.status) {
            case 403:
              message = '没有权限访问该资源'
              break
            case 404:
              message = '请求资源不存在'
              break
            case 500:
              message = '服务器内部错误'
              break
            default:
              message = `请求错误: ${error.response.status}`
          }
        } else {
          message = error.message || '网络连接异常'
        }
        console.error('请求错误:', message)
        return Promise.reject(error)
      },
    )
  }

  // 公共请求方法
  request(config) {
    return this.instance.request(config)
  }

  // GET请求
  get(url, params = {}) {
    return this.instance.get(url, { params })
  }

  // POST请求
  post(url, data = {}) {
    return this.instance.post(url, data)
  }

  // PUT请求
  put(url, data = {}) {
    return this.instance.put(url, data)
  }

  // DELETE请求
  delete(url, params = {}) {
    return this.instance.delete(url, { params })
  }
}

// 创建并导出实例
export const http = new Http()

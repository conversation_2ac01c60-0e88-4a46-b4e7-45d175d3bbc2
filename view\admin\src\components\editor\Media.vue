<script setup>
import { defineProps, defineEmits, computed, ref, reactive } from 'vue'
import {
  UploadOutlined,
  EllipsisOutlined,
  EyeOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import Simplebar from 'simplebar-vue'

const emit = defineEmits(['update:modelValue', 'update:fileList', 'video-select', 'remove-video'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  showButton: {
    type: Boolean,
    default: false,
  },
  fileList: {
    type: Array,
    default: () => [],
  },
  maxCount: {
    type: Number,
    default: 8,
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value),
  },
})

const open = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  },
})
const fileList = computed({
  get() {
    return props.fileList
  },
  set(val) {
    emit('update:fileList', val)
  },
})
// 计算组件尺寸
const componentSize = computed(() => {
  const sizeMap = {
    small: {
      width: 60,
      height: 60,
      fontSize: 16,
      textSize: 10,
    },
    default: {
      width: 100,
      height: 100,
      fontSize: 24,
      textSize: 12,
    },
    large: {
      width: 120,
      height: 120,
      fontSize: 28,
      textSize: 14,
    },
  }
  return sizeMap[props.size] || sizeMap.default
})

// 计算上传列表的类名
const uploadListClass = computed(() => {
  const totalItems = props.fileList.length + (props.fileList.length < props.maxCount ? 1 : 0)
  return {
    'has-single-item': totalItems === 1,
    'has-multiple-items': totalItems > 1,
  }
})

const confirmLoading = ref(false)
const selectedVideo = ref([])
const currentPage = ref(1)
const isGroup = ref(false)

const form = reactive({
  keyword: '',
  sort: 'asc',
})

// 模拟图片数据
const imageList = ref([
  {
    id: 1,
    url: 'https://picsum.photos/200/200?random=1',
    name: '图片1.jpg',
    size: '125KB',
  },
  {
    id: 2,
    url: 'https://picsum.photos/200/200?random=2',
    name: '图片2.jpg',
    size: '98KB',
  },
  {
    id: 3,
    url: 'https://picsum.photos/200/200?random=3',
    name: '图片3.jpg',
    size: '156KB',
  },
  {
    id: 4,
    url: 'https://picsum.photos/200/200?random=4',
    name: '图片4.jpg',
    size: '203KB',
  },
  {
    id: 5,
    url: 'https://picsum.photos/200/200?random=5',
    name: '图片5.jpg',
    size: '178KB',
  },
  {
    id: 6,
    url: 'https://picsum.photos/200/200?random=6',
    name: '图片6.jpg',
    size: '134KB',
  },
])

const handleUpload = (info) => {
  if (info.file.status !== 'uploading') {
    console.log(info.file, info.fileList)
  }
  if (info.file.status === 'done') {
    message.success(`${info.file.name} file uploaded successfully`)
  } else if (info.file.status === 'error') {
    message.error(`${info.file.name} file upload failed.`)
  }
}
const onSearch = () => {}

// 打开上传模态框
const openUpload = () => {
  open.value = true
}

// 选择视频
const selectVideo = (index) => {
  if (selectedVideo.value.includes(index)) {
    selectedVideo.value = selectedVideo.value.filter((i) => i !== index)
  } else {
    selectedVideo.value.push(index)
  }
}

// 预览视频
const handlePreview = (item) => {
  console.log('预览视频:', item)
}

// 删除视频
const removeVideo = (index) => {
  emit('remove-video', index)
}

// 处理确定按钮
const handleOk = () => {
  if (selectedVideo.value.length > 0) {
    const selectedVideos = selectedVideo.value.map((index) => imageList.value[index])
    emit('video-select', selectedVideos)
  }
  selectedVideo.value = []
  open.value = false
}

// 处理取消按钮
const handleCancel = () => {
  selectedVideo.value = []
  open.value = false
}
</script>
<template>
  <!-- 自定义上传按钮组件 -->
  <div v-if="showButton" class="custom-upload-container">
    <div class="upload-list" :class="uploadListClass">
      <!-- 已上传的视频 -->
      <div
        v-for="(item, index) in fileList"
        :key="item.uid"
        class="upload-list-item"
        :style="{
          width: componentSize.width + 'px',
          height: componentSize.height + 'px',
        }"
      >
        <div class="upload-list-item-thumbnail">
          <video
            :src="item.thumbUrl || item.url"
            :width="componentSize.width"
            :height="componentSize.height"
            style="object-fit: cover"
            muted
            preload="metadata"
          />
          <div class="upload-list-item-actions">
            <a-button type="text" size="small" @click="handlePreview(item)" class="action-btn">
              <EyeOutlined />
            </a-button>
            <a-button type="text" size="small" @click="removeVideo(index)" class="action-btn">
              <DeleteOutlined />
            </a-button>
          </div>
        </div>
      </div>

      <!-- 上传按钮 -->
      <div
        v-if="fileList.length < maxCount"
        class="upload-select"
        @click="openUpload"
        :style="{
          width: componentSize.width + 'px',
          height: componentSize.height + 'px',
        }"
      >
        <UploadOutlined :style="{ fontSize: componentSize.fontSize + 'px' }" />
        <div :style="{ marginTop: '8px', fontSize: componentSize.textSize + 'px' }">上传视频</div>
      </div>
    </div>
  </div>

  <a-modal
    v-model:open="open"
    title="视频媒体"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="860px"
    ok-text="确定"
    :ok-button-props="{ disabled: selectedVideo.length <= 0 }"
    wrapClassName="uploader"
  >
    <div class="container">
      <div class="slider">
        <simplebar class="albums">
          <div class="album-item active">
            <div class="name"><i class="icon"></i><span>默认</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div class="album-item">
            <div class="name"><i class="icon"></i><span>自定义</span></div>
            <a-dropdown>
              <EllipsisOutlined @click.prevent />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">重命名</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">删除</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </simplebar>
        <a-button @click="isGroup = true">创建分组</a-button>
      </div>
      <div class="panel">
        <div class="panel-header">
          <a-upload v-model:file-list="fileList" name="file" @change="handleUpload">
            <a-button type="primary">
              <upload-outlined></upload-outlined>
              上传
            </a-button>
          </a-upload>
          <a-form :model="form" layout="inline">
            <a-form-item>
              <a-input-search
                v-model:value="form.keyword"
                placeholder="关键词"
                style="width: 200px"
                @search="onSearch"
              />
            </a-form-item>
            <a-form-item label="排序">
              <a-select v-model:value="form.sort" style="width: 130px">
                <a-select-option value="asc">时间从先到后</a-select-option>
                <a-select-option value="desc">时间从后到先</a-select-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>
        <simplebar class="panel-body">
          <a-space :size="[8, 8]" wrap>
            <template v-for="(item, index) in imageList" :key="index">
              <div
                :class="[selectedVideo.includes(index) ? 'active' : '']"
                @click="selectVideo(index)"
              >
                <a-image :src="item.url" :width="100" :height="100" :preview="false" />
              </div>
            </template>
          </a-space>
        </simplebar>
        <div class="panel-footer">
          <div class="action">
            <div>
              已选择<span>{{ selectedVideo.length }}</span
              >项
            </div>
            <div class="ant-button-group">
              <a-button>删除</a-button>
              <a-button>移动</a-button>
              <a-button>复制ID</a-button>
            </div>
          </div>
          <a-pagination
            show-less-items
            :show-size-changer="false"
            v-model:current="currentPage"
            :total="500"
          />
        </div>
      </div>
    </div>
  </a-modal>
  <a-modal v-model:open="isGroup" title="创建分组" width="300px" centered>
    <a-form
      :labelCol="{
        style: {
          width: '70px',
        },
      }"
      labelAlign="left"
      layout="vertical"
    >
      <a-form-item label="分组名称">
        <a-input></a-input>
      </a-form-item>
      <a-form-item label="上级分组">
        <a-select>
          <a-select-option value="1">顶级分组</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="排序">
        <a-input></a-input>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<style lang="less" scoped>
.container {
  display: flex;
  .slider {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;
    width: 156px;
    border-right: 1px solid #ebecef;
    .albums {
      display: flex;
      flex-direction: column;
      padding-top: 6px;
      height: 370px;
      padding-right: 16px;
      .album-item {
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &.active {
          background-color: #f1f4f9;
          border-radius: 6px;
        }
        .name {
          display: flex;
          align-items: center;
          gap: 4px;
          cursor: pointer;
        }
        .icon {
          width: 20px;
          height: 20px;
          display: inline-block;
          background: url(@/assets/icons/folder.png) no-repeat left top;
          background-size: 100%;
        }
        .anticon {
          font-size: 18px;
        }
      }
    }
    .ant-btn {
      width: auto;
      margin-right: 16px;
    }
  }
  .panel {
    flex: 1;
    padding: 0px;
    padding-left: 16px;
    .panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 16px;
      :deep(.ant-form-item) {
        .ant-input-search {
          .ant-input-group {
            position: relative !important;
          }
          .ant-input {
            border-radius: 6px;
            padding-right: 32px;
          }
          .ant-input-group-addon {
            background: none;
          }
          .ant-input-search-button {
            border: none;
            background: none;
            box-shadow: none;
            position: absolute;
            right: 0;
            top: 0;
            z-index: 1;
          }
        }
        &:last-child {
          margin-inline-end: 0px;
        }
      }
    }
    .panel-body {
      height: 320px;
      :deep(.ant-image-img) {
        border-radius: 4px;
        transition: all 0.1s;
      }
      .active {
        position: relative;
        overflow: hidden; // 隐藏超出部分
        z-index: 1;
        border-radius: 4px;
        :deep(.ant-image-img) {
          border-radius: 4px;
          border: 2px solid #1677ff;
        }
        &::after {
          content: ' ';
          position: absolute;
          right: -18px;
          bottom: -18px;
          width: 0;
          height: 0;
          border: 18px solid transparent;
          border-bottom-color: #1677ff;
          transform: rotate(135deg);
          z-index: 2;
        }
        &::before {
          content: ' ';
          position: absolute;
          right: 2px; // 调整位置
          bottom: 6px; // 调整位置
          width: 8px; // 对号宽度
          height: 4px; // 对号高度
          border: 2px solid #fff;
          border-top: none;
          border-right: none;
          transform: rotate(-45deg);
          z-index: 3;
        }
      }
    }
    .panel-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 16px;
      .action {
        display: flex;
        align-items: center;
        gap: 12px;
        span {
          color: #1677ff;
        }
        .ant-button-group {
          .ant-btn {
            padding: 4px 10px;
            &:not(:last-child) {
              border-right: 0;
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }
            &:not(:first-child) {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
            }
            &:hover {
              border-color: #d9d9d9;
            }
          }
        }
      }
    }
  }
}

// 自定义上传容器样式
.custom-upload-container {
  width: max-content;
  max-width: max-content;
  // 全局处理视频错误状态
  :deep(.video.video-error) {
    display: none !important;
  }

  .upload-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 8px;

    // 多个元素时左对齐
    &.has-multiple-items {
      justify-content: flex-start;
      align-items: flex-start;
    }

    // 单个元素时无间距，保持居中
    &.has-single-item {
      gap: 0;
    }

    .upload-list-item {
      position: relative;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      overflow: hidden;
      background-color: #fafafa;
      box-sizing: border-box;

      .upload-list-item-thumbnail {
        width: 100%;
        height: 100%;
        position: relative;

        video {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .upload-list-item-actions {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          opacity: 0;
          transition: opacity 0.3s;

          .action-btn {
            color: white;
            border: none;

            &:hover {
              color: #1890ff;
              background: rgba(255, 255, 255, 0.2);
            }
          }
        }

        &:hover .upload-list-item-actions {
          opacity: 1;
        }
      }
    }

    .upload-select {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: border-color 0.3s;
      color: #666;
      box-sizing: border-box;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }
}
</style>

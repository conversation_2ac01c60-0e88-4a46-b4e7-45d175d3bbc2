<?php

declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class Plugin extends Command
{
    // 插件模板文件内容
    protected $templates = [];

    protected function configure()
    {
        // 指令配置
        $this->setName('plugin')
            ->addArgument('name', Argument::REQUIRED, 'Plugin name')
            ->addOption('force', 'f', Option::VALUE_NONE, 'Overwrite existing files')
            ->addOption('author', null, Option::VALUE_OPTIONAL, 'Plugin author', 'Your Name')
            ->addOption('description', null, Option::VALUE_OPTIONAL, 'Plugin description', 'A Xshop plugin')
            ->setDescription('Create a new plugin structure');
    }

    protected function execute(Input $input, Output $output)
    {
        $pluginName = $input->getArgument('name');
        $force = $input->getOption('force');
        $author = $input->getOption('author');
        $description = $input->getOption('description');

        // 验证插件名称
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9]*$/', $pluginName)) {
            $output->writeln('<error>Plugin name must be alphanumeric and start with a letter</error>');
            return 0;
        }

        // 初始化模板内容
        $this->initTemplates($pluginName, $author, $description);

        // 创建插件目录结构
        $this->createPluginStructure($pluginName, $force, $output);

        $output->writeln('<info>Plugin "' . $pluginName . '" created successfully!</info>');
        $output->writeln('<comment>Location: plugins/' . $pluginName . '/</comment>');

        return 1;
    }

    /**
     * 初始化模板内容
     */
    protected function initTemplates($pluginName, $author, $description)
    {
        $namespace = 'plugins\\' . $pluginName;
        $currentDate = date('Y-m-d H:i:s');

        // Plugin.php 模板
        $this->templates['Plugin.php'] = <<<PHP
<?php
namespace {$namespace};

use plugins\Plugin as BasePlugin;

class Plugin extends BasePlugin
{
    /**
     * 插件信息
     */
    public function info()
    {
        return [
            'name'        => '{$pluginName}',
            'title'       => '{$pluginName} Plugin',
            'description' => '{$description}',
            'author'      => '{$author}',
            'version'     => '1.0.0',
            'created_at' => '{$currentDate}',
            'updated_at' => '{$currentDate}'
        ];
    }

    /**
     * 安装方法
     */
    public function install()
    {
        // 执行安装操作
        // 例如：创建数据库表、初始化数据等
        return true;
    }

    /**
     * 卸载方法
     */
    public function uninstall()
    {
        // 执行卸载操作
        // 注意：谨慎处理数据删除
        return true;
    }

    /**
     * 启用插件
     */
    public function enable()
    {
        // 插件启用时的逻辑
        return true;
    }

    /**
     * 禁用插件
     */
    public function disable()
    {
        // 插件禁用时的逻辑
        return true;
    }
}
PHP;

        // 控制器模板
        $this->templates['controller/Index.php'] = <<<PHP
<?php
namespace {$namespace}\controller;

use think\facade\View;

class Index
{
    /**
     * 插件首页
     */
    public function index()
    {
        return View::fetch('index', [
            'title' => '{$pluginName} Plugin Home'
        ]);
    }

    /**
     * 插件配置页面
     */
    public function config()
    {
        return View::fetch('config', [
            'title' => '{$pluginName} Plugin Configuration'
        ]);
    }
}
PHP;

        // 路由文件模板
        $this->templates['route.php'] = <<<PHP
<?php
use think\\facade\\Route;

// {$pluginName} 插件路由定义
Route::group('{$pluginName}', function () {
    // 插件首页
    Route::get('index', '{$namespace}\\controller\\Index/index');
    
    // 配置页面
    Route::get('config', '{$namespace}\\controller\\Index/config');
    
    // API 接口示例
    Route::group('api', function () {
        Route::get('data', '{$namespace}\\controller\\Index/getData');
        Route::post('save', '{$namespace}\\controller\\Index/saveData');
    });
})->allowCrossDomain();
PHP;

        // 配置文件模板
        $this->templates['config/config.php'] = <<<PHP
<?php
// {$pluginName} 插件配置
return [
    'plugin_name' => '{$pluginName}',
    'version'     => '1.0.0',
    'author'      => '{$author}',
    'settings'    => [
        'enable' => true,
        'api_key' => '',
        'secret' => ''
    ]
];
PHP;

        // 事件配置文件模板
        $this->templates['config/event.php'] = <<<PHP
<?php
// {$pluginName} 插件事件定义
return [
    // 应用初始化事件
    'AppInit' => [
        // '{$namespace}\\event\\AppInit'
    ],
    
    // Http运行结束事件
    'HttpEnd' => [
        // '{$namespace}\\event\\HttpEnd'
    ]
];
PHP;

        // 视图文件模板 - 首页
        $this->templates['view/index.vue'] = <<<HTML
<template>
    <div class="container">
        <div class="header">
            <h1>{\$title}</h1>
            <p>Welcome to your {$pluginName} plugin!</p>
        </div>
        
        <div class="content">
            <h2>Plugin Information</h2>
            <ul>
                <li>Plugin Name: {$pluginName}</li>
                <li>Version: 1.0.0</li>
                <li>Author: {$author}</li>
            </ul>
        </div>
    </div>
</template>
<script steup>
    
</script>
<style scoped></style>
HTML;

        // 视图文件模板 - 配置页
        $this->templates['view/config.vue'] = <<<HTML
<template>
    <h1>{\$title}</h1>
    <p>This is the configuration page for {$pluginName} plugin.</p>
    
    <form method="post">
        <div>
            <label>API Key:</label>
            <input type="text" name="api_key">
        </div>
        
        <div>
            <label>Secret:</label>
            <input type="password" name="secret">
        </div>
        
        <button type="submit">Save Settings</button>
    </form>
</template>
<script steup>
    
</script>
<style scoped></style>
HTML;

        // 安装SQL模板
        $this->templates['install.sql'] = <<<SQL
-- {$pluginName} Plugin Installation SQL
-- Created: {$currentDate}

-- 创建插件配置表
CREATE TABLE IF NOT EXISTS `plugin_{$pluginName}_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `value` text,
  `created_at` timestamp DEFAULT NULL,
  `updated_at` timestamp DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入默认配置
INSERT INTO `plugin_{$pluginName}_settings` (`name`, `value`, `created_at`, `updated_at`) VALUES
('version', '1.0.0', NOW(), NOW()),
('status', 'active', NOW(), NOW());

-- 创建插件数据表示例
CREATE TABLE IF NOT EXISTS `plugin_{$pluginName}_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text,
  `created_at` timestamp DEFAULT NULL,
  `updated_at` timestamp DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
SQL;

        // 卸载SQL模板
        $this->templates['uninstall.sql'] = <<<SQL
-- {$pluginName} Plugin Uninstallation SQL
-- WARNING: This will remove all plugin data!

DROP TABLE IF EXISTS `plugin_{$pluginName}_settings`;
DROP TABLE IF EXISTS `plugin_{$pluginName}_data`;
SQL;

        // README 文件
        $this->templates['README.md'] = <<<MD
# {$pluginName} Plugin

{$description}

## Installation

1. Place this plugin in the `plugins/` directory
2. Run the installation command:
   \`\`\`bash
   php think plugin:install {$pluginName}
   \`\`\`

## Configuration

Edit the configuration file at `plugins/{$pluginName}/config/config.php`

## Usage

Access the plugin at: `http://your-domain.com/{$pluginName}/index`

## Author

{$author}

## License

MIT License
MD;
    }

    /**
     * 创建插件目录结构
     */
    protected function createPluginStructure($pluginName, $force, $output)
    {
        $basePath = base_path('plugins/' . $pluginName . '/');

        // 定义需要创建的目录
        $directories = [
            '',
            'controller',
            'model',
            'view',
            'config',
            'event',
            'lang'
        ];

        // 创建目录
        foreach ($directories as $dir) {
            $dirPath = $basePath . $dir;
            if (!is_dir($dirPath)) {
                mkdir($dirPath, 0755, true);
                $output->writeln('<comment>Created directory: ' . $dirPath . '</comment>');
            }
        }

        // 创建文件
        foreach ($this->templates as $relativePath => $content) {
            $filePath = $basePath . $relativePath;

            // 检查文件是否已存在
            if (file_exists($filePath) && !$force) {
                $output->writeln('<error>File already exists: ' . $filePath . ' (use --force to overwrite)</error>');
                continue;
            }

            // 确保目录存在
            $dir = dirname($filePath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }

            // 写入文件
            if (file_put_contents($filePath, $content)) {
                $output->writeln('<info>Created file: ' . $filePath . '</info>');
            } else {
                $output->writeln('<error>Failed to create file: ' . $filePath . '</error>');
            }
        }
    }
}

<script setup>
import { createFromIconfontCN } from '@ant-design/icons-vue'
const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_5001432_mxaujwlu0ws.js', // 在 iconfont.cn 上生成
})

const props = defineProps({
  prefix: {
    type: String,
    default: 'icon',
  },
  name: {
    type: String,
    required: true,
  },
  size: {
    type: String,
    default: '16px',
  },
})
</script>
<template>
  <icon-font :type="props.prefix + '-' + props.name" :style="{ fontSize: props.size }" />
</template>
<style lang="less" scoped>
.anticon {
  vertical-align: -0.2em !important;
}
</style>

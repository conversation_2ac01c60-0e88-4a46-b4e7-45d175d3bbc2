<?php

namespace plugins;

use think\App;

abstract class Plugin
{
    protected $app;

    public function __construct(App $app)
    {
        $this->app = $app;
    }

    // 插件信息
    abstract public function info();

    // 安装方法
    public function install()
    {
        return true;
    }

    // 卸载方法
    public function uninstall()
    {
        return true;
    }

    // 启动方法
    public function boot()
    {
        // 插件启动逻辑
    }

    // 启用方法
    public function enable()
    {
        // 插件启用时的逻辑
    }

    // 禁用方法
    public function disable()
    {
        // 插件禁用时的逻辑
    }
}

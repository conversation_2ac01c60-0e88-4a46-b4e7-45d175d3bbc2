<?php

declare(strict_types=1);

namespace app\admin\controller;

use think\Request;

class Plugin extends Admin
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index()
    {
        $pluginService = app('plugin');
        $list = $pluginService->getPlugins();
        return $this->success(['plugins' => $list], '');
    }

    public function enable($name)
    {
        $pluginService = app('plugin');
        if ($pluginService->enable($name)) {
            return json(['code' => 200, 'msg' => '启用成功']);
        }
        return json(['code' => 500, 'msg' => '启用失败']);
    }

    public function disable($name)
    {
        // 类似enable方法
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create()
    {
        //
    }

    /**
     * 保存新建的资源
     *
     * @param  \think\Request  $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        //
    }

    /**
     * 显示指定的资源
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function read($id)
    {
        //
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * 保存更新的资源
     *
     * @param  \think\Request  $request
     * @param  int  $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * 删除指定资源
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function delete($id)
    {
        //
    }
}

-- demo Plugin Installation SQL
-- Created: 2025-08-25 16:56:15

-- 创建插件配置表
CREATE TABLE IF NOT EXISTS `plugin_demo_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `value` text,
  `created_at` timestamp DEFAULT NULL,
  `updated_at` timestamp DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入默认配置
INSERT INTO `plugin_demo_settings` (`name`, `value`, `created_at`, `updated_at`) VALUES
('version', '1.0.0', NOW(), NOW()),
('status', 'active', NOW(), NOW());

-- 创建插件数据表示例
CREATE TABLE IF NOT EXISTS `plugin_demo_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `content` text,
  `created_at` timestamp DEFAULT NULL,
  `updated_at` timestamp DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
<script setup>
import { reactive } from 'vue'
import brand from '@/assets/brand.svg'
const form = reactive({
  username: '',
  password: '',
  remember: false,
})
</script>
<template>
  <div class="container">
    <a-space wrap>
      <div class="logo">
        <img :src="brand" width="80" />
      </div>
      <a-form :model="form" size="large" layout="vertical">
        <h1>欢迎登录</h1>
        <p>管理控制台登录</p>
        <a-form-item
          name="username"
          :rules="[{ required: true, message: 'Please input your username!' }]"
        >
          <a-input v-model:value="form.username" placeholder="账号" />
        </a-form-item>
        <a-form-item
          name="password"
          :rules="[{ required: true, message: 'Please input your password!' }]"
        >
          <a-input-password v-model:value="form.password" placeholder="密码" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" size="large" class="login-btn">登录</a-button>
        </a-form-item>
        <a-form-item name="remember">
          <a-checkbox v-model:checked="form.remember">记住登录状态</a-checkbox>
        </a-form-item>
      </a-form>
    </a-space>
  </div>
</template>
<style lang="less" scoped>
.container {
  height: 100%;
  background-color: #f0f5ff;
  overflow: hidden;

  :deep(.ant-space) {
    width: 100%;
    height: 100%;

    .ant-space-item {
      position: relative;
      margin-bottom: 0;
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      &:first-child {
        background: url('@/assets/bg.svg') no-repeat left top;
        background-size: 60%;
        background-position: center;
      }
      &:last-child {
        background-color: #ffffff;
      }
    }

    .logo {
      position: absolute;
      left: 60px;
      top: 60px;
      font-size: 24px;
      font-weight: 600;
    }

    .ant-form {
      min-width: 300px;
      background-color: #fff;

      p {
        color: #999;
      }

      .ant-input,
      .ant-input-password {
        border: none;
        border-radius: 6px;
        background-color: #e9ecef;
        &:focus {
          box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.5);
        }
      }
      .ant-input-affix-wrapper-focused {
        box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.5);
      }

      .login-btn {
        width: 100%;
        border-radius: 6px;
      }
    }
  }
}
</style>

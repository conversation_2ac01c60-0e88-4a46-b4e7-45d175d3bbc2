<script setup>
import { reactive, ref } from 'vue'
import { Cascader } from 'ant-design-vue'
import AddGoods from './component/AddGoods.vue'

const addModel = ref(false)
const selectedValue = ref([])
const form = reactive({
  goodsName: '',
  goodsCode: '',
  categoryId: ['2'],
  status: 0,
})

const options = [
  {
    value: 'zhejiang',
    label: 'Zhejiang',
    children: [
      {
        value: 'hangzhou',
        label: 'Hangzhou',
        children: [
          {
            value: 2,
            label: 'West Lake',
          },
        ],
      },
    ],
  },
  {
    value: 'jiangsu',
    label: 'Jiangsu',
    children: [
      {
        value: 'nanjing',
        label: 'Nanjing',
        children: [
          {
            value: 1,
            label: 'Zhong Hua Men',
          },
        ],
      },
    ],
  },
]

const dataSource = ref([
  {
    key: '1',
    name: '胡彦斌',
    age: 32,
    address: '西湖区湖底公园1号',
  },
  {
    key: '2',
    name: '胡彦祖',
    age: 42,
    address: '西湖区湖底公园1号',
  },
])
const columns = ref([
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '住址',
    dataIndex: 'address',
    key: 'address',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 180,
  },
])
// 自定义显示渲染，只显示最后一级
const displayRender = ({ labels }) => {
  return labels.pop() || ''
}
const onCategoryChanged = (val) => {
  if (val) {
    form.categoryId = [...val].pop()
  } else {
    form.categoryId = 0
  }
}
</script>
<template>
  <div class="container">
    <div class="toolbar">
      <a-space class="w-full">
        <a-form :model="form" layout="inline">
          <a-form-item label="商品名称"><a-input v-model:value="form.goodsName" /></a-form-item>
          <a-form-item label="商品编码"><a-input v-model="form.goodsCode" /></a-form-item>
          <a-form-item label="商品分类"
            ><a-cascader
              v-model:value="selectedValue"
              :options="options"
              placeholder="请选择分类"
              style="min-width: 230px"
              @change="onCategoryChanged"
              :display-render="displayRender"
          /></a-form-item>
          <a-form-item><a-button type="primary">搜索</a-button></a-form-item>
        </a-form>
      </a-space>
      <a-space>
        <a-radio-group v-model:value="form.status">
          <a-radio-button :value="0">全部</a-radio-button>
          <a-radio-button :value="1">出售中</a-radio-button>
          <a-radio-button :value="2">已下架</a-radio-button>
          <a-radio-button :value="3">已售罄</a-radio-button>
        </a-radio-group>

        <a-button type="primary" @click="addModel = true">新增</a-button>
        <a-button type="default">批量删除</a-button>
        <a-button type="default">批量下架</a-button>
      </a-space>
    </div>
    <a-table :dataSource="dataSource" :columns="columns">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <span>
            <a>编辑</a>
            <a-divider type="vertical" />
            <a>删除</a>
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                <span>更多</span>
                <icon-font name="arrow-down" size="12px" />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">上架</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">下架</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">售罄</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </span>
        </template>
      </template>
    </a-table>
  </div>
  <AddGoods v-model="addModel" />
</template>
<style lang="less" scoped>
.container {
  width: 100%;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: #fff;
  .toolbar {
    display: flex;
    flex-direction: column;
    gap: 24px;
    .ant-form {
      padding: 12px;
      border-radius: 6px;
      background-color: #f5f7fa;
    }
    .ant-radio-group {
      margin-right: 24px;
    }
    :deep(.ant-space) {
      &.w-full {
        .ant-space-item {
          width: 100% !important;
        }
      }
    }
  }
  .ant-dropdown-link {
    .anticon {
      color: #999;
      position: relative;
      left: 2px;
      top: -2px;
      vertical-align: middle;
    }
  }
}
</style>

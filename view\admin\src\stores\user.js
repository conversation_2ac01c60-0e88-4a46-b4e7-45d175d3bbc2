import { authApi } from '@/api/auth'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: null,
    userInfo: null,
  }),
  getters: {
    isAuthenticated: (state) => !!state.token,
  },
  actions: {
    // 设置token
    setToken(token) {
      this.token = token
    },

    // 清除token
    clearToken() {
      this.token = ''
      this.userInfo = null
    },

    // 登录
    async login(credentials) {
      try {
        const response = await authApi.login(credentials)
        this.setToken(response.data.token)
        await this.getUserInfo()
        return Promise.resolve(response)
      } catch (error) {
        this.clearToken()
        return Promise.reject(error)
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await authApi.getUserInfo()
        this.userInfo = response.data
        return Promise.resolve(response)
      } catch (error) {
        return Promise.reject(error)
      }
    },

    // 退出登录
    logout() {
      this.clearToken()
    },
  },
  persist: true,
})

<?php

declare(strict_types=1);

namespace app;

use app\service\PluginService;
use think\Service;

/**
 * 应用服务类
 */
class AppService extends Service
{
    public function register()
    {
        // 插件服务注册
        $this->app->register(PluginService::class);
    }

    public function boot()
    {
        //  boot方法在应用启动时被调用
        // 获取当前的路由配置（通常为route/app.php）
        $routeConfig = $this->app->config->get('route', []);
        // 追加admin.php路由文件路径到配置中
        $routeConfig['route_config_files'][] = base_path('route/api.php');
        $routeConfig['route_config_files'][] = base_path('route/admin.php');
        // 重新设置路由配置
        $this->app->config->set($routeConfig, 'route');
    }
}

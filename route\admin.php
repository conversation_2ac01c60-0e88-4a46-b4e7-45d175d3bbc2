<?php
// +----------------------------------------------------------------------
// | 后台路由定义
// +----------------------------------------------------------------------
use think\facade\Route;

// 后台登录页
Route::get('admin/login', 'admin/Login/index');
// 处理登录逻辑
Route::post('admin/login/check', 'admin/Login/check');

// 使用分组统一管理后台路由，并添加中间件（如权限验证）
Route::group('admin', function () {
    // 后台首页
    Route::get('index', 'admin.Index/index');
    // 用户管理资源路由
    Route::resource('user', 'admin.User');
    // 文章管理资源路由
    Route::resource('article', 'admin.Article');
})->middleware(\app\admin\middleware\Auth::class);

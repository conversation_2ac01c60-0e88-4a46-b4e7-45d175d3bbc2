<script setup>
import { ref, computed, onMounted } from 'vue'
import { MenuFoldOutlined, MenuUnfoldOutlined, RedoOutlined } from '@ant-design/icons-vue'
import Simplebar from 'simplebar-vue'
import 'simplebar-vue/dist/simplebar.min.css'
import { useRoute, useRouter } from 'vue-router'
import { useMenuStore } from '@/stores/menus'
const route = useRoute()
const router = useRouter()
const menuStore = useMenuStore()
const menus = router.options.routes.filter((item) => item.name == 'home').shift()
const currentRoute = ref(null)
//菜单当前选中项
const selectedKeys = computed({
  get: () => {
    return menuStore.selectedKeys
  },
  set: (val) => {
    menuStore.selectedKeys = val
  },
})
//子菜单当前选中项
const subSelectedKeys = computed({
  get: () => {
    return menuStore.subSelectedKeys
  },
  set: (val) => {
    menuStore.subSelectedKeys = val
  },
})
//侧边栏折叠
const collapsed = computed({
  get: () => {
    return menuStore.collapsed
  },
  set: (val) => {
    menuStore.collapsed = val
  },
})
//生成导航面包屑
const breadcrumbs = computed(() => {
  const matched = route.matched
    .filter((item) => item.meta && item.meta.type != 'button')
    .map((item) => {
      // 处理动态面包屑名称
      if (item.meta.title) {
        return {
          path: item.path,
          breadcrumbName: item.meta.title,
        }
      }
      return {
        path: item.path,
        breadcrumbName: item.name || item.path,
      }
    })

  // 确保首页始终存在
  if (matched.length === 0 || matched[0].path !== '/') {
    matched.unshift({
      path: '/',
      breadcrumbName: '首页',
    })
  }

  return matched
})
//获取当前菜单的子菜单
const subMenus = computed(() => {
  // eslint-disable-next-line vue/no-side-effects-in-computed-properties
  currentRoute.value = menus.children.find((menu) => route.path.startsWith(menu.path))
  // 递归转换菜单项
  const getChildrens = (items) => {
    if (!Array.isArray(items)) return []
    return items
      .filter((item) => item.meta && item.meta.type !== 'button')
      .map((item) => {
        const menuItem = {
          key: item.name || item.path,
          label: item.meta?.title || item.name || item.path,
          title: item.meta?.title || item.name || item.path,
        }

        // 如果有子菜单，递归处理
        if (item.children && item.children.length > 0) {
          menuItem.children = getChildrens(item.children)
        }

        return menuItem
      })
  }
  return getChildrens(currentRoute.value.children)
})
//菜单点击事件
const handleMenuClick = (e) => {
  selectedKeys.value = [e.key]
  router.push({ name: e.key })
}
//点击子菜单
const handleSubMenuClick = (e) => {
  subSelectedKeys.value = [e.key]
  router.push({ name: e.key })
}

onMounted(() => {})
</script>

<template>
  <a-layout>
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      width="130px"
      collapsedWidth="70px"
      collapsible
    >
      <div class="logo">
        <img src="@/assets/logo.svg" width="24" /><span class="brand">管理控制台</span>
      </div>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        theme="dark"
        mode="inline"
        @click="handleMenuClick"
      >
        <a-menu-item :key="menu.name" v-for="menu in menus.children">
          <icon-font :name="menu.meta.icon" />
          <span>{{ menu.meta.title }}</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-header style="background: #fff; padding: 0">
        <div class="top-navbar-left">
          <menu-unfold-outlined
            v-if="collapsed"
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />
          <menu-fold-outlined v-else class="trigger" @click="() => (collapsed = !collapsed)" />
          <RedoOutlined class="refresh" />
        </div>
        <div class="top-navbar-right">
          <li>
            <a href=""> <icon-font name="bell" size="18px" /><span>消息</span> </a>
          </li>
          <li>
            <a href=""> <icon-font name="app" size="18px" /><span>应用</span> </a>
          </li>
          <li>
            <a href=""> <icon-font name="store" size="18px" /><span>预览</span> </a>
          </li>
          <li>
            <a-dropdown placement="top" arrow
              ><a href=""
                ><a-avatar
                  src="https://plus.unsplash.com/premium_vector-1719858610423-17bb8a3b3f9d?q=80&w=880&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                /><span>Admin</span></a
              ><template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;">管理账号</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">清除缓存</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;">安全退出</a>
                  </a-menu-item>
                </a-menu>
              </template></a-dropdown
            >
          </li>
        </div>
      </a-layout-header>
      <a-layout-content>
        <div class="app-sidemenu" v-if="subMenus.length > 0">
          <span>{{ currentRoute.meta.title }}</span>
          <a-menu
            mode="inline"
            :items="subMenus"
            :inlineIndent="12"
            v-model:selectedKeys="subSelectedKeys"
            @click="handleSubMenuClick"
          ></a-menu>
        </div>
        <simplebar class="app-container">
          <a-page-header :title="route.meta.title">
            <template #breadcrumb>
              <a-breadcrumb separator=">">
                <a-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index">
                  <router-link v-if="index < breadcrumbs.length - 1" :to="item.path">
                    {{ item.breadcrumbName }}
                  </router-link>
                  <span v-else>{{ item.breadcrumbName }}</span>
                </a-breadcrumb-item>
              </a-breadcrumb>
            </template>
          </a-page-header>
          <div class="app-content"><RouterView /></div>
          <div class="app-copyright">Copyright © 2025 Xshop. All Rights Reserved</div>
        </simplebar>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>
<style lang="less" scoped>
.app-container {
  flex: 1;
  height: calc(100vh - 64px);
  overflow: auto;
  :deep(.simplebar-scrollbar::before) {
    background-color: #bebfc2;
  }
}

.app-content {
  padding: 0 24px;
  //减去顶部导航栏-pageheader-copyright
  min-height: calc(100vh - 64px - 152px);
}

.logo {
  height: 32px;
  margin: 16px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;

  .anticon {
    font-size: 20px;
  }

  .brand {
    white-space: nowrap;
  }
}

.trigger,
.refresh {
  font-size: 18px;
  line-height: 64px;
  padding-left: 24px;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: #1890ff;
  }
}
.ant-layout-sider {
  .ant-menu {
    .ant-menu-item {
      .ant-menu-title-content {
        .anticon {
          font-size: 16px;
        }
      }
    }
  }
}
.ant-layout-content {
  display: flex;
  height: 100%;
  .app-sidemenu {
    background-color: #fff;
    border-inline-end: 1px solid rgba(5, 5, 5, 0.06);
    > span {
      height: 40px;
      line-height: 40px;
      display: block;
      font-size: 18px;
      font-weight: 600;
      padding-inline: 16px;
      margin-inline: 4px;
      margin-block: 4px;
      border-bottom: 1px solid #f2f3f7;
    }
    :deep(.ant-menu-light) {
      border-inline-end: none;
      min-width: 150px;
      .ant-menu-item-selected,
      .ant-menu-item-active {
        background: none;
        font-weight: bold;
      }
      .ant-menu-sub {
        background: none;
      }
    }
  }
}
.ant-layout-header {
  display: flex;
  justify-content: space-between;
  border-block-end: 1px solid rgba(5, 5, 5, 0.06);

  .top-navbar-right {
    display: flex;
    gap: 24px;
    padding-right: 24px;

    li {
      list-style: none;

      a {
        color: inherit;

        &:hover {
          color: #1890ff;
        }

        .anticon {
          font-size: 18px;
          margin-right: 6px;
        }
      }

      .ant-dropdown-trigger {
        display: flex;
        gap: 6px;
        align-items: center;
        flex-shrink: 0;
      }
    }
  }
}

:deep(.ant-dropdown-menu) {
  .ant-dropdown-menu-title-content > a {
    &:hover {
      color: #1890ff;
    }
  }
}

.ant-layout-sider-collapsed {
  .logo {
    span.brand {
      display: none;
    }
  }
}
:deep(.ant-breadcrumb) {
  a {
    color: rgba(0, 0, 0, 0.88);
    &:hover {
      color: rgba(0, 0, 0, 0.88);
      background: none;
    }
  }
  li {
    &:last-child {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
:deep(.ant-card) {
  border-radius: 0;
  box-shadow: none;
  .ant-card-head {
    border: none;
    padding: 12px 24px;
    min-height: auto;
  }
  .ant-card-body {
    padding-top: 0;
  }
}
.app-copyright {
  color: #929292;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 12px;
}
</style>

{"name": "admin", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2": "^5.3.5", "@vueup/vue-quill": "^1.2.0", "ant-design-vue": "4.x", "axios": "^1.11.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "simplebar-vue": "^2.4.2", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "fast-glob": "^3.3.3", "globals": "^16.3.0", "less": "^4.4.0", "prettier": "3.6.2", "unplugin-vue-components": "^29.0.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-eslint-parser": "10.0.0"}}
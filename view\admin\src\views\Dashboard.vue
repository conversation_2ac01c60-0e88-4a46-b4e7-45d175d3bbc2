<script setup>
import { onMounted, ref } from 'vue'
import { Chart } from '@antv/g2'

const visitContainer = ref(null)
const tradeContainer = ref(null)

onMounted(() => {
  renderVisitChart(visitContainer.value)
  renderTradeChart(tradeContainer.value)
})

function renderVisitChart(container) {
  const chart = new Chart({
    container,
    autoFit: true,
    height: 286,
  })

  // 声明可视化
  chart.options({
    type: 'view',
    autoFit: true,
    data: [
      { month: 'Jan', city: 'Tokyo', temperature: 7 },
      { month: 'Jan', city: 'London', temperature: 3.9 },
      { month: 'Feb', city: 'Tokyo', temperature: 6.9 },
      { month: 'Feb', city: 'London', temperature: 4.2 },
      { month: 'Mar', city: 'Tokyo', temperature: 9.5 },
      { month: 'Mar', city: 'London', temperature: 5.7 },
      { month: 'Apr', city: 'Tokyo', temperature: 14.5 },
      { month: 'Apr', city: 'London', temperature: 8.5 },
      { month: 'May', city: 'Tokyo', temperature: 18.4 },
      { month: 'May', city: 'London', temperature: 11.9 },
      { month: 'Jun', city: 'Tokyo', temperature: 21.5 },
      { month: 'Jun', city: 'London', temperature: 15.2 },
      { month: 'Jul', city: 'Tokyo', temperature: 25.2 },
      { month: 'Jul', city: 'London', temperature: 17 },
      { month: 'Aug', city: 'Tokyo', temperature: 26.5 },
      { month: 'Aug', city: 'London', temperature: 16.6 },
      { month: 'Sep', city: 'Tokyo', temperature: 23.3 },
      { month: 'Sep', city: 'London', temperature: 14.2 },
      { month: 'Oct', city: 'Tokyo', temperature: 18.3 },
      { month: 'Oct', city: 'London', temperature: 10.3 },
      { month: 'Nov', city: 'Tokyo', temperature: 13.9 },
      { month: 'Nov', city: 'London', temperature: 6.6 },
      { month: 'Dec', city: 'Tokyo', temperature: 9.6 },
      { month: 'Dec', city: 'London', temperature: 4.8 },
    ],
    encode: { x: 'month', y: 'temperature', shape: 'smooth' },
    axis: {
      y: {
        title: null,
        labelFormatter: (d) => d + '°C',
      },
      x: {
        title: null,
      },
    },
    children: [
      {
        type: 'line',
      },
      { type: 'point', encode: { shape: 'circle' } },
    ],
    style: {
      lineWidth: 2,
    },
  })
  // 渲染可视化
  chart.render()
}

function renderTradeChart(container) {
  const chart = new Chart({
    container,
    autoFit: true,
    height: 286,
  })

  // 声明可视化
  chart.options({
    type: 'view',
    autoFit: true, //是否自适应容器宽高
    data: [
      { day: '2015/9/1', share: 10 },
      { day: '2015/9/2', share: 12 },
      { day: '2015/9/3', share: 11 },
      { day: '2015/9/4', share: 15 },
      { day: '2015/9/5', share: 20 },
      { day: '2015/9/6', share: 22 },
      { day: '2015/9/7', share: 21 },
      { day: '2015/9/8', share: 25 },
      { day: '2015/9/9', share: 31 },
      { day: '2015/9/10', share: 32 },
      { day: '2015/9/11', share: 28 },
      { day: '2015/9/12', share: 29 },
      { day: '2015/9/13', share: 40 },
      { day: '2015/9/14', share: 41 },
      { day: '2015/9/15', share: 45 },
      { day: '2015/9/16', share: 50 },
      { day: '2015/9/17', share: 65 },
      { day: '2015/9/18', share: 45 },
      { day: '2015/9/19', share: 50 },
      { day: '2015/9/20', share: 51 },
      { day: '2015/9/21', share: 65 },
      { day: '2015/9/22', share: 60 },
      { day: '2015/9/23', share: 62 },
      { day: '2015/9/24', share: 65 },
      { day: '2015/9/25', share: 45 },
      { day: '2015/9/26', share: 55 },
      { day: '2015/9/27', share: 59 },
      { day: '2015/9/28', share: 52 },
      { day: '2015/9/29', share: 53 },
      { day: '2015/9/30', share: 40 },
    ],
    encode: { x: 'day', y: 'share', shape: 'smooth' },
    axis: {
      y: {
        title: null,
      },
      x: {
        title: null,
        tickFilter: (_, i) => i % 2 !== 0, // 过滤 x 轴刻度线，只显示每隔 1 个刻度线
        labelFormatter: (d) => {
          const date = new Date(d)
          return `${date.getMonth() + 1}/${date.getDate()}` // 格式化为 "月/日"
        },
      },
    },
    children: [
      {
        type: 'area',
        style: {
          opacity: 0.1,
          fill: '#1ebf49',
        },
      },
      {
        type: 'line',
        style: {
          lineWidth: 3,
          stroke: '#1ebf49',
        },
      },
      { type: 'point', encode: { shape: 'circle' }, style: { stroke: '#1ebf49', fill: '#1ebf49' } },
    ],
  })
  // 渲染可视化
  chart.render()
}
</script>
<template>
  <a-space direction="vertical" size="large" style="width: 100%">
    <div class="panel">
      <h3 class="panel-head">代办事项</h3>
      <a-space size="middle">
        <div class="todo-item">
          <div class="icon"><icon-font name="daifukuan" size="32px" /></div>
          <div class="data">
            <label for="">待付款订单</label>
            <span>274</span>
          </div>
        </div>
        <div class="todo-item">
          <div class="icon" style="background-color: #ffb53e">
            <icon-font name="daifahuo" size="32px" />
          </div>
          <div class="data">
            <label for="">待发货订单</label>
            <span>274</span>
          </div>
        </div>
        <div class="todo-item">
          <div class="icon" style="background-color: #1ebf49">
            <icon-font name="shouhoudingdan" size="32px" />
          </div>
          <div class="data">
            <label for="">待售后订单</label>
            <span>274</span>
          </div>
        </div>
        <div class="todo-item">
          <div class="icon" style="background-color: #ff445b">
            <icon-font name="daipingjia1" size="32px" />
          </div>
          <div class="data">
            <label for="">待回复订单</label>
            <span>274</span>
          </div>
        </div>
      </a-space>
    </div>
    <div class="panel">
      <h3 class="panel-head">实时数据</h3>
      <div class="data-list">
        <a-row :gutter="[24, 24]">
          <a-col :span="8">
            <div class="data-list-item">
              <div class="box-left">
                <div class="icon"><icon-font name="qianfang" size="24px" /></div>
                <div>
                  <label>支付金额</label>
                  <span>0</span>
                </div>
              </div>
              <div class="box-right">
                <label for="">昨天全天 89</label>
                <div>日环比 <span class="up">-100%</span></div>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="data-list-item">
              <div class="box-left">
                <div class="icon"><icon-font name="fangke" size="24px" /></div>
                <div>
                  <label>访客人数</label>
                  <span>0</span>
                </div>
              </div>
              <div class="box-right">
                <label for="">昨天全天 89</label>
                <div>日环比 <span class="down">-100%</span></div>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="data-list-item">
              <div class="box-left">
                <div class="icon"><icon-font name="user" size="24px" /></div>
                <div>
                  <label>支付人数</label>
                  <span>0</span>
                </div>
              </div>
              <div class="box-right">
                <label for="">昨天全天 89</label>
                <div>日环比 <span class="down">-100%</span></div>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="data-list-item">
              <div class="box-left">
                <div class="icon"><icon-font name="eye" size="24px" /></div>
                <div>
                  <label>浏览量</label>
                  <span>0</span>
                </div>
              </div>
              <div class="box-right">
                <label for="">昨天全天 89</label>
                <div>日环比 <span class="down">-100%</span></div>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="data-list-item">
              <div class="box-left">
                <div class="icon"><icon-font name="finance" size="24px" /></div>
                <div>
                  <label>付款订单</label>
                  <span>0</span>
                </div>
              </div>
              <div class="box-right">
                <label for="">昨天全天 89</label>
                <div>日环比 <span class="down">-100%</span></div>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="data-list-item">
              <div class="box-left">
                <div class="icon"><icon-font name="shouhoudingdan" size="24px" /></div>
                <div>
                  <label>退款订单</label>
                  <span>0</span>
                </div>
              </div>
              <div class="box-right">
                <label for="">昨天全天 89</label>
                <div>日环比 <span class="down">-100%</span></div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
    <div class="panel charts">
      <div class="chart">
        <div class="panel-head">访问趋势</div>
        <div class="chart" ref="visitContainer"></div>
      </div>
      <div class="chart">
        <div class="panel-head">交易趋势</div>
        <div class="chart" ref="tradeContainer"></div>
      </div>
    </div>
  </a-space>
</template>
<style lang="less" scoped>
:deep(.ant-space) {
  margin-bottom: 0px;
  width: 100%;
  .ant-space-item {
    flex: 1;
  }
}
.todo-item {
  display: flex;
  background-color: #f7f8fa;
  border-radius: 4px;
  overflow: hidden;
  .icon {
    width: 80px;
    height: 80px;
    line-height: 80px;
    background-color: #1890ff;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    .anticon {
      color: #fff;
      font-size: 28px;
    }
  }
  .data {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    label {
      color: rgba(0, 0, 0, 0.6);
    }
    span {
      font-size: 18px;
      font-weight: bold;
    }
  }
}
.data-list {
  .data-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &:hover {
      &::after {
        content: ' ';
        position: absolute;
        top: -6px;
        left: -6px;
        right: -6px;
        bottom: -6px;
        border-radius: 4px;
        background-color: #f5f7fa;
      }
    }

    .box-left {
      display: flex;
      align-items: center;
      gap: 12px;
      z-index: 1;

      .icon {
        width: 64px;
        height: 64px;
        border-radius: 4px;
        background-color: rgba(0, 0, 0, 0.03);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      div {
        display: flex;
        flex-direction: column;
        span {
          font-size: 18px;
          font-weight: 600;
        }
      }
    }
    .box-right {
      font-size: 12px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      z-index: 1;
      label {
        color: #999;
      }
      div {
        > span {
          position: relative;
          padding-right: 12px;
          &.down {
            &::after {
              content: ' ';
              top: 50%;
              right: 0px;
              position: absolute;
              transform: translateY(-50%);
              border-left: 4px solid transparent;
              border-right: 4px solid transparent;
              border-top: 4px solid #ff445b;
            }
          }
          &.up {
            &::after {
              content: ' ';
              top: 50%;
              right: 0px;
              position: absolute;
              transform: translateY(-50%);
              border-left: 4px solid transparent;
              border-right: 4px solid transparent;
              border-bottom: 4px solid #1ebf49;
            }
          }
        }
      }
    }
  }
}
.charts {
  display: flex;
  > div {
    flex: 1;
  }
}
</style>

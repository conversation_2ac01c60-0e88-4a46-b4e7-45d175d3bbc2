<script setup>
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { computed, ref } from 'vue'
import Upload from './Upload.vue'
import Media from './Media.vue'

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
})

const content = computed({
  get() {
    return typeof props.modelValue === 'string' ? props.modelValue : String(props.modelValue || '')
  },
  set(val) {
    emit('update:modelValue', typeof val === 'string' ? val : String(val))
  },
})

// Album组件相关
const isUpload = ref(false)
const isMediaUpload = ref(false)
const quillEditor = ref(null)
let instance = null

// 自定义工具栏配置
const toolbarOptions = [
  ['bold', 'italic', 'underline', 'strike'],
  ['blockquote', 'code-block'],
  [{ header: 1 }, { header: 2 }],
  [{ list: 'ordered' }, { list: 'bullet' }],
  [{ script: 'sub' }, { script: 'super' }],
  [{ indent: '-1' }, { indent: '+1' }],
  [{ direction: 'rtl' }],
  [{ size: ['small', false, 'large', 'huge'] }],
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  [{ color: [] }, { background: [] }],
  [{ font: [] }],
  [{ align: [] }],
  ['clean'],
  ['link', 'image', 'video'],
]

// 处理编辑器准备就绪
const handleReady = (quill) => {
  instance = quill
  const toolbar = quill.getModule('toolbar')
  toolbar.addHandler('image', () => uploadFile('image'))
  toolbar.addHandler('video', () => uploadFile('video'))
}

// 打开相册
const uploadFile = (type) => {
  if (type === 'image') {
    isUpload.value = true
  } else {
    isMediaUpload.value = true
  }
}

// 处理图片选择
const handleImageSelect = (selectedImages) => {
  if (!instance || !selectedImages) {
    isUpload.value = false
    return
  }

  const processImage = (imageItem) => {
    let imageUrl = null
    let imageAlt = ''

    if (typeof imageItem === 'string') {
      imageUrl = imageItem
    } else if (typeof imageItem === 'number') {
      imageUrl = imageItem.url
    } else if (imageItem && typeof imageItem === 'object') {
      imageUrl = imageItem.url || imageItem.src || imageItem.href || imageItem.link
      imageAlt = imageItem.name || imageItem.alt || imageItem.title || ''
    }

    if (imageUrl) {
      insertMedia('image', imageUrl, { alt: imageAlt })
    }
  }

  if (Array.isArray(selectedImages)) {
    selectedImages.forEach(processImage)
  } else {
    processImage(selectedImages)
  }

  isUpload.value = false
}

// 处理视频选择
const handleVideoSelect = (selectedVideos) => {
  if (!instance || !selectedVideos) {
    isMediaUpload.value = false
    return
  }

  const processVideo = (videoItem) => {
    let videoUrl = null
    let videoTitle = ''

    if (typeof videoItem === 'string') {
      videoUrl = videoItem
    } else if (typeof videoItem === 'number') {
      videoUrl = videoItem.url
    } else if (videoItem && typeof videoItem === 'object') {
      videoUrl = videoItem.url || videoItem.src || videoItem.href || videoItem.link
      videoTitle = videoItem.name || videoItem.title || videoItem.alt || ''
    }

    if (videoUrl) {
      insertMedia('video', videoUrl, { title: videoTitle })
    }
  }

  if (Array.isArray(selectedVideos)) {
    selectedVideos.forEach(processVideo)
  } else {
    processVideo(selectedVideos)
  }

  isMediaUpload.value = false
}

// 通用媒体插入函数
const insertMedia = (mediaType, mediaUrl, attributes = {}) => {
  if (!instance || !mediaUrl) return

  const range = instance.getSelection(true)
  const insertIndex = range ? range.index : instance.getLength()

  try {
    switch (mediaType) {
      case 'image':
        instance.insertEmbed(insertIndex, 'image', mediaUrl)
        instance.insertText(insertIndex + 1, '\n')
        instance.setSelection(insertIndex + 2)
        break
      case 'video':
        instance.insertEmbed(insertIndex, 'video', mediaUrl)
        instance.insertText(insertIndex + 1, '\n')
        instance.setSelection(insertIndex + 2)
        break
      case 'link': {
        const linkText = attributes.text || mediaUrl
        instance.insertText(insertIndex, linkText, 'link', mediaUrl)
        instance.setSelection(insertIndex + linkText.length)
        break
      }
    }

    // 插入后强制同步HTML内容
    setTimeout(() => {
      const htmlContent = instance.root.innerHTML
      if (typeof htmlContent === 'string') {
        emit('update:modelValue', htmlContent)
      }
    }, 100)
  } catch (error) {
    console.error(`Error inserting ${mediaType}:`, error)
  }
}
</script>
<template>
  <div class="editor">
    <QuillEditor
      ref="quillEditor"
      theme="snow"
      :toolbar="toolbarOptions"
      v-model:content="content"
      :placeholder="props.placeholder"
      contentType="html"
      @ready="handleReady"
    />

    <!-- upload组件 -->
    <upload v-model="isUpload" @image-select="handleImageSelect" />
    <media v-model="isMediaUpload" @video-select="handleVideoSelect" />
  </div>
</template>
<style lang="less" scoped>
.editor {
  :deep(.ql-toolbar) {
    &.ql-snow {
      padding: 4px 8px;
      border-start-start-radius: 6px;
      border-start-end-radius: 6px;
      border-color: #d9d9d9;
      background-color: #f9fbff;
      .ql-active {
        border-radius: 4px;
      }
    }
  }
  :deep(.ql-container) {
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.12);
    &.ql-snow {
      border-color: #d9d9d9;
      border-end-start-radius: 6px;
      border-end-end-radius: 6px;
    }
    .ql-editor {
      position: relative;
      margin: 24px auto;
      width: 375px;
      min-height: 420px;
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 10px rgba(0, 0, 0, 0.25);
      &.ql-blank::before {
        font-style: normal;
      }
    }
  }
}
</style>

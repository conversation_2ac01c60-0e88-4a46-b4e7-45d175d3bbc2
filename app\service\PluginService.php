<?php

namespace app\service;

use think\Service;
use think\facade\Cache;
use think\facade\Config;
use think\facade\Event;

class PluginService extends Service
{
    protected $pluginPath;

    /**
     * register 方法中注册服务绑定
     */
    public function register()
    {
        // 绑定插件服务到容器，方便其他地方调用
        $this->app->bind('plugin', PluginService::class);

        // 注册插件路径常量（可选）
        if (!defined('PLUGIN_PATH')) {
            define('PLUGIN_PATH', base_path() . 'plugins' . DIRECTORY_SEPARATOR);
        }

        $this->pluginPath = PLUGIN_PATH;
    }

    /**
     * boot 方法在应用启动时调用，用于加载插件
     */
    public function boot()
    {
        // 加载所有已启用插件的路由、事件等
        $this->loadEnabledPlugins();
    }

    /**
     * 加载所有已启用的插件
     */
    protected function loadEnabledPlugins()
    {
        $enabledPlugins = $this->getEnabledPlugins();

        foreach ($enabledPlugins as $pluginName) {
            $this->loadPlugin($pluginName);
        }
    }

    /**
     * 加载单个插件
     */
    protected function loadPlugin($pluginName)
    {
        $pluginClass = "plugins\\{$pluginName}\\Plugin";

        if (class_exists($pluginClass)) {
            $pluginInstance = new $pluginClass($this->app);

            // 1. 注册插件路由
            $this->loadPluginRoutes($pluginName);

            // 2. 注册插件事件（如果插件有定义）
            $this->loadPluginEvents($pluginName);

            // 3. 注册插件中间件（如果插件有定义）
            $this->loadPluginMiddlewares($pluginName);

            // 4. 执行插件的启动方法
            if (method_exists($pluginInstance, 'boot')) {
                $pluginInstance->boot($this->app);
            }
        }
    }

    /**
     * 加载插件的路由文件
     */
    protected function loadPluginRoutes($pluginName)
    {
        $routeFile = $this->pluginPath . "{$pluginName}/route.php";

        if (is_file($routeFile)) {
            include $routeFile;
        }

        // 或者也可以使用配置方式加载路由
        $configRouteFile = $this->pluginPath . "{$pluginName}/config/route.php";
        if (is_file($configRouteFile)) {
            $routes = include $configRouteFile;
            if (is_array($routes)) {
                $this->registerConfigRoutes($routes);
            }
        }
    }

    /**
     * 注册配置文件中的路由
     */
    protected function registerConfigRoutes($routes)
    {
        foreach ($routes as $route => $config) {
            // 这里可以根据配置动态注册路由
            // 例如: ['blog/index' => 'plugins/Blog/controller/Index/index']
        }
    }

    /**
     * 加载插件的事件定义
     */
    protected function loadPluginEvents($pluginName)
    {
        $eventFile = $this->pluginPath . "{$pluginName}/config/event.php";

        if (is_file($eventFile)) {
            $events = include $eventFile;
            if (is_array($events)) {
                foreach ($events as $event => $listeners) {
                    foreach ((array)$listeners as $listener) {
                        Event::listen($event, $listener);
                    }
                }
            }
        }
    }

    /**
     * 加载插件的中间件定义
     */
    protected function loadPluginMiddlewares($pluginName)
    {
        $middlewareFile = $this->pluginPath . "{$pluginName}/config/middleware.php";

        if (is_file($middlewareFile)) {
            $middlewares = include $middlewareFile;
            if (is_array($middlewares)) {
                // 这里可以将中间件注册到全局或特定的路由
                // 具体实现取决于你的中间件管理策略
            }
        }
    }

    /**
     * 获取所有已启用的插件列表
     */
    public function getEnabledPlugins()
    {
        // 从缓存或数据库获取已启用的插件列表
        // 这里简化为从配置文件读取
        $statusFile = $this->pluginPath . 'plugin_status.php';
        $statusList = is_file($statusFile) ? include $statusFile : [];

        return array_keys(array_filter($statusList));
    }

    /**
     * 获取所有插件信息（包括未启用的）
     */
    public function getPlugins()
    {
        return Cache::remember('plugins', function () {
            $pluginDirs = glob($this->pluginPath . '*', GLOB_ONLYDIR);
            $list = [];

            foreach ($pluginDirs as $dir) {
                $pluginName = basename($dir);
                $pluginInfo = $this->getPluginInfo($pluginName);
                if ($pluginInfo) {
                    $list[$pluginName] = $pluginInfo;
                }
            }

            return $list;
        }, 3600);
    }

    /**
     * 获取单个插件信息
     */
    public function getPluginInfo($pluginName)
    {
        $class = "plugins\\{$pluginName}\\Plugin";

        if (!class_exists($class)) {
            return null;
        }

        $pluginInstance = new $class($this->app);
        $info = [
            'name' => $pluginName,
            'class' => $class,
            'instance' => $pluginInstance,
            'enabled' => $this->isEnabled($pluginName)
        ];

        // 如果插件有info方法，获取详细信息
        if (method_exists($pluginInstance, 'info')) {
            $info = array_merge($info, $pluginInstance->info());
        }

        return $info;
    }

    /**
     * 启用插件
     */
    public function enable($pluginName)
    {
        $pluginInfo = $this->getPluginInfo($pluginName);
        if (!$pluginInfo) {
            throw new \Exception("插件 {$pluginName} 不存在");
        }

        // 执行插件安装方法
        if (method_exists($pluginInfo['instance'], 'install')) {
            $pluginInfo['instance']->install();
        }

        // 更新插件状态
        $this->setPluginStatus($pluginName, true);

        // 清除缓存
        Cache::delete('plugins');

        return true;
    }

    /**
     * 禁用插件
     */
    public function disable($pluginName)
    {
        $pluginInfo = $this->getPluginInfo($pluginName);
        if (!$pluginInfo) {
            throw new \Exception("插件 {$pluginName} 不存在");
        }

        // 执行插件卸载方法
        if (method_exists($pluginInfo['instance'], 'uninstall')) {
            $pluginInfo['instance']->uninstall();
        }

        // 更新插件状态
        $this->setPluginStatus($pluginName, false);

        // 清除缓存
        Cache::delete('plugins');

        return true;
    }

    /**
     * 检查插件是否启用
     */
    protected function isEnabled($pluginName)
    {
        $statusFile = $this->pluginPath . 'plugin_status.php';
        $statusList = is_file($statusFile) ? include $statusFile : [];

        return !empty($statusList[$pluginName]);
    }

    /**
     * 设置插件状态
     */
    protected function setPluginStatus($pluginName, $status)
    {
        $statusFile = $this->pluginPath . 'plugin_status.php';
        $statusList = is_file($statusFile) ? include $statusFile : [];
        $statusList[$pluginName] = $status;

        file_put_contents($statusFile, "<?php\nreturn " . var_export($statusList, true) . ";");
    }
}

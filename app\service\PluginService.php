<?php

namespace app\service;

use think\Service;
use think\facade\Cache;
use think\facade\Config;
use think\facade\Event;
use think\facade\Route;

class PluginService extends Service
{
    protected $pluginPath;

    /**
     * register 方法中注册服务绑定
     */
    public function register()
    {
        // 绑定插件服务到容器，方便其他地方调用
        $this->app->bind('plugin', PluginService::class);

        // 注册插件路径常量（可选）
        if (!defined('PLUGIN_PATH')) {
            define('PLUGIN_PATH', base_path() . 'plugins' . DIRECTORY_SEPARATOR);
        }

        $this->pluginPath = PLUGIN_PATH;
    }

    /**
     * boot 方法在应用启动时调用，用于加载插件
     */
    public function boot()
    {
        // 加载所有已启用插件的路由、事件等
        $this->loadEnabledPlugins();
    }

    /**
     * 加载所有已启用的插件
     */
    protected function loadEnabledPlugins()
    {
        $enabledPlugins = $this->getEnabledPlugins();

        foreach ($enabledPlugins as $pluginName) {
            $this->loadPlugin($pluginName);
        }
    }

    /**
     * 加载单个插件
     */
    protected function loadPlugin($pluginName)
    {
        $pluginClass = "plugins\\{$pluginName}\\Plugin";

        if (class_exists($pluginClass)) {
            $pluginInstance = new $pluginClass($this->app);

            // 1. 注册插件路由
            $this->loadPluginRoutes($pluginName);

            // 2. 注册插件事件（如果插件有定义）
            $this->loadPluginEvents($pluginName);

            // 3. 注册插件中间件（如果插件有定义）
            $this->loadPluginMiddlewares($pluginName);

            // 4. 执行插件的启动方法
            if (method_exists($pluginInstance, 'boot')) {
                $pluginInstance->boot($this->app);
            }
        }
    }

    /**
     * 加载插件的路由文件
     */
    protected function loadPluginRoutes($pluginName)
    {
        // 1. 优先加载传统的 route.php 文件（直接包含，使用 Route facade）
        $routeFile = $this->pluginPath . "{$pluginName}/route.php";
        if (is_file($routeFile)) {
            include $routeFile;
        }

        // 2. 加载配置式路由文件 config/route.php（返回数组配置）
        $configRouteFile = $this->pluginPath . "{$pluginName}/config/route.php";
        if (is_file($configRouteFile)) {
            $routes = include $configRouteFile;
            if (is_array($routes)) {
                $this->registerConfigRoutes($routes, $pluginName);
            }
        }
    }

    /**
     * 注册配置文件中的路由
     *
     * @param array $routes 路由配置数组
     * @param string $pluginName 插件名称
     */
    protected function registerConfigRoutes($routes, $pluginName = '')
    {
        foreach ($routes as $routeConfig) {
            $this->registerSingleRoute($routeConfig, $pluginName);
        }
    }

    /**
     * 注册单个路由配置
     *
     * @param array $config 路由配置
     * @param string $pluginName 插件名称
     */
    protected function registerSingleRoute($config, $pluginName = '')
    {
        // 路由配置格式示例:
        // [
        //     'method' => 'get|post|put|delete|any',
        //     'rule' => 'blog/index',
        //     'route' => 'plugins\Blog\controller\Index/index',
        //     'option' => [
        //         'middleware' => ['auth', 'cors'],
        //         'domain' => 'api',
        //         'ext' => 'html',
        //         'https' => true,
        //         'complete_match' => false,
        //         'cache' => 3600,
        //         'ajax' => true,
        //         'pjax' => false,
        //         'json' => false,
        //         'validate' => [],
        //         'filter' => [],
        //         'append' => [],
        //         'model' => '',
        //         'auto_search' => false
        //     ]
        // ]

        if (!isset($config['method']) || !isset($config['rule']) || !isset($config['route'])) {
            return;
        }

        $method = strtolower($config['method']);
        $rule = $config['rule'];
        $route = $config['route'];
        $option = $config['option'] ?? [];

        // 如果提供了插件名称，为路由规则添加插件前缀（如果还没有的话）
        if ($pluginName && strpos($rule, $pluginName . '/') !== 0) {
            $rule = $pluginName . '/' . ltrim($rule, '/');
        }

        // 创建路由对象
        $routeObj = null;

        switch ($method) {
            case 'get':
                $routeObj = Route::get($rule, $route);
                break;
            case 'post':
                $routeObj = Route::post($rule, $route);
                break;
            case 'put':
                $routeObj = Route::put($rule, $route);
                break;
            case 'delete':
                $routeObj = Route::delete($rule, $route);
                break;
            case 'patch':
                $routeObj = Route::patch($rule, $route);
                break;
            case 'any':
                $routeObj = Route::any($rule, $route);
                break;
            case 'resource':
                $routeObj = Route::resource($rule, $route);
                break;
            default:
                // 支持自定义方法组合，如 'get|post'
                $routeObj = Route::rule($rule, $route, $method);
                break;
        }

        if ($routeObj) {
            // 应用路由选项
            if (!empty($option)) {
                $this->applyRouteOptions($routeObj, $option);
            }

            // 自动应用路由中间件（基于路由模式匹配）
            $routeMiddlewares = $this->getRouteMiddlewares($rule, $pluginName);
            if (!empty($routeMiddlewares)) {
                foreach ($routeMiddlewares as $middleware) {
                    $routeObj->middleware($middleware);
                }
            }
        }
    }

    /**
     * 应用路由选项
     */
    protected function applyRouteOptions($routeObj, $options)
    {
        foreach ($options as $key => $value) {
            switch ($key) {
                case 'middleware':
                    if (is_array($value)) {
                        foreach ($value as $middleware) {
                            $routeObj->middleware($middleware);
                        }
                    } else {
                        $routeObj->middleware($value);
                    }
                    break;
                case 'domain':
                    $routeObj->domain($value);
                    break;
                case 'ext':
                    $routeObj->ext($value);
                    break;
                case 'https':
                    if ($value) {
                        $routeObj->https();
                    }
                    break;
                case 'complete_match':
                    $routeObj->completeMatch($value);
                    break;
                case 'cache':
                    $routeObj->cache($value);
                    break;
                case 'ajax':
                    if ($value) {
                        $routeObj->ajax();
                    }
                    break;
                case 'pjax':
                    if ($value) {
                        $routeObj->pjax();
                    }
                    break;
                case 'json':
                    if ($value) {
                        $routeObj->json();
                    }
                    break;
                case 'validate':
                    if (!empty($value)) {
                        $routeObj->validate($value);
                    }
                    break;
                case 'filter':
                    if (!empty($value)) {
                        $routeObj->filter($value);
                    }
                    break;
                case 'append':
                    if (!empty($value)) {
                        $routeObj->append($value);
                    }
                    break;
                case 'model':
                    if (!empty($value)) {
                        $routeObj->model($value);
                    }
                    break;
                case 'auto_search':
                    if ($value) {
                        $routeObj->autoSearch();
                    }
                    break;
                case 'allow_cross_domain':
                    if ($value) {
                        $routeObj->allowCrossDomain();
                    }
                    break;
                case 'deny_cross_domain':
                    if ($value) {
                        $routeObj->denyCrossDomain();
                    }
                    break;
                case 'header':
                    if (!empty($value)) {
                        $routeObj->header($value);
                    }
                    break;
                case 'pattern':
                    if (!empty($value)) {
                        foreach ($value as $var => $rule) {
                            $routeObj->pattern($var, $rule);
                        }
                    }
                    break;
            }
        }
    }

    /**
     * 加载插件的事件定义
     */
    protected function loadPluginEvents($pluginName)
    {
        $eventFile = $this->pluginPath . "{$pluginName}/config/event.php";

        if (is_file($eventFile)) {
            $events = include $eventFile;
            if (is_array($events)) {
                foreach ($events as $event => $listeners) {
                    foreach ((array)$listeners as $listener) {
                        Event::listen($event, $listener);
                    }
                }
            }
        }
    }

    /**
     * 加载插件的中间件定义
     */
    protected function loadPluginMiddlewares($pluginName)
    {
        $middlewareFile = $this->pluginPath . "{$pluginName}/config/middleware.php";

        if (is_file($middlewareFile)) {
            $middlewares = include $middlewareFile;
            if (is_array($middlewares)) {
                $this->registerPluginMiddlewares($middlewares, $pluginName);
            }
        }
    }

    /**
     * 注册插件中间件
     */
    protected function registerPluginMiddlewares($middlewares, $pluginName)
    {
        // 中间件配置格式示例:
        // [
        //     'global' => [
        //         'plugins\Blog\middleware\Auth',
        //         'plugins\Blog\middleware\Log'
        //     ],
        //     'alias' => [
        //         'blog_auth' => 'plugins\Blog\middleware\Auth',
        //         'blog_log' => 'plugins\Blog\middleware\Log'
        //     ],
        //     'group' => [
        //         'blog' => [
        //             'plugins\Blog\middleware\Auth',
        //             'plugins\Blog\middleware\Log'
        //         ]
        //     ],
        //     'route' => [
        //         'blog/admin/*' => ['blog_auth', 'blog_log'],
        //         'blog/api/*' => ['blog_auth']
        //     ]
        // ]

        // 注册全局中间件
        if (isset($middlewares['global']) && is_array($middlewares['global'])) {
            $this->registerGlobalMiddlewares($middlewares['global']);
        }

        // 注册中间件别名
        if (isset($middlewares['alias']) && is_array($middlewares['alias'])) {
            $this->registerMiddlewareAliases($middlewares['alias']);
        }

        // 注册中间件分组
        if (isset($middlewares['group']) && is_array($middlewares['group'])) {
            $this->registerMiddlewareGroups($middlewares['group']);
        }

        // 注册路由中间件（这个需要在路由注册时处理，这里先存储）
        if (isset($middlewares['route']) && is_array($middlewares['route'])) {
            $this->storeRouteMiddlewares($middlewares['route'], $pluginName);
        }
    }

    /**
     * 注册全局中间件
     */
    protected function registerGlobalMiddlewares($middlewares)
    {
        $globalMiddlewares = $this->app->config->get('middleware.global', []);

        foreach ($middlewares as $middleware) {
            if (!in_array($middleware, $globalMiddlewares)) {
                $globalMiddlewares[] = $middleware;
            }
        }

        $this->app->config->set(['global' => $globalMiddlewares], 'middleware');
    }

    /**
     * 注册中间件别名
     */
    protected function registerMiddlewareAliases($aliases)
    {
        $middlewareAliases = $this->app->config->get('middleware.alias', []);

        foreach ($aliases as $alias => $middleware) {
            $middlewareAliases[$alias] = $middleware;
        }

        $this->app->config->set(['alias' => $middlewareAliases], 'middleware');
    }

    /**
     * 注册中间件分组
     */
    protected function registerMiddlewareGroups($groups)
    {
        $middlewareGroups = $this->app->config->get('middleware.group', []);

        foreach ($groups as $group => $middlewares) {
            if (!isset($middlewareGroups[$group])) {
                $middlewareGroups[$group] = [];
            }

            foreach ($middlewares as $middleware) {
                if (!in_array($middleware, $middlewareGroups[$group])) {
                    $middlewareGroups[$group][] = $middleware;
                }
            }
        }

        $this->app->config->set(['group' => $middlewareGroups], 'middleware');
    }

    /**
     * 存储路由中间件配置（用于后续路由注册时使用）
     */
    protected function storeRouteMiddlewares($routeMiddlewares, $pluginName)
    {
        $storedRouteMiddlewares = Cache::get('plugin_route_middlewares', []);

        foreach ($routeMiddlewares as $routePattern => $middlewares) {
            $key = $pluginName . ':' . $routePattern;
            $storedRouteMiddlewares[$key] = $middlewares;
        }

        Cache::set('plugin_route_middlewares', $storedRouteMiddlewares, 3600);
    }

    /**
     * 获取路由对应的中间件
     */
    public function getRouteMiddlewares($route, $pluginName = null)
    {
        $storedRouteMiddlewares = Cache::get('plugin_route_middlewares', []);
        $middlewares = [];

        foreach ($storedRouteMiddlewares as $key => $routeMiddlewares) {
            list($plugin, $pattern) = explode(':', $key, 2);

            // 如果指定了插件名，只匹配该插件的中间件
            if ($pluginName && $plugin !== $pluginName) {
                continue;
            }

            // 简单的路由模式匹配（支持通配符 *）
            if ($this->matchRoutePattern($route, $pattern)) {
                $middlewares = array_merge($middlewares, (array)$routeMiddlewares);
            }
        }

        return array_unique($middlewares);
    }

    /**
     * 路由模式匹配
     */
    protected function matchRoutePattern($route, $pattern)
    {
        // 将通配符 * 转换为正则表达式
        $regex = str_replace(['/', '*'], ['\/', '.*'], $pattern);
        $regex = '/^' . $regex . '$/i';

        return preg_match($regex, $route);
    }

    /**
     * 获取所有已启用的插件列表
     */
    public function getEnabledPlugins()
    {
        // 从缓存或数据库获取已启用的插件列表
        // 这里简化为从配置文件读取
        $statusFile = $this->pluginPath . 'plugin_status.php';
        $statusList = is_file($statusFile) ? include $statusFile : [];

        return array_keys(array_filter($statusList));
    }

    /**
     * 获取所有插件信息（包括未启用的）
     */
    public function getPlugins()
    {
        return Cache::remember('plugins', function () {
            $pluginDirs = glob($this->pluginPath . '*', GLOB_ONLYDIR);
            $list = [];

            foreach ($pluginDirs as $dir) {
                $pluginName = basename($dir);
                $pluginInfo = $this->getPluginInfo($pluginName);
                if ($pluginInfo) {
                    $list[$pluginName] = $pluginInfo;
                }
            }

            return $list;
        }, 3600);
    }

    /**
     * 获取单个插件信息
     */
    public function getPluginInfo($pluginName)
    {
        $class = "plugins\\{$pluginName}\\Plugin";

        if (!class_exists($class)) {
            return null;
        }

        $pluginInstance = new $class($this->app);
        $info = [
            'name' => $pluginName,
            'class' => $class,
            'instance' => $pluginInstance,
            'enabled' => $this->isEnabled($pluginName)
        ];

        // 如果插件有info方法，获取详细信息
        if (method_exists($pluginInstance, 'info')) {
            $info = array_merge($info, $pluginInstance->info());
        }

        return $info;
    }

    /**
     * 启用插件
     */
    public function enable($pluginName)
    {
        $pluginInfo = $this->getPluginInfo($pluginName);
        if (!$pluginInfo) {
            throw new \Exception("插件 {$pluginName} 不存在");
        }

        // 执行插件安装方法
        if (method_exists($pluginInfo['instance'], 'install')) {
            $pluginInfo['instance']->install();
        }

        // 更新插件状态
        $this->setPluginStatus($pluginName, true);

        // 清除缓存
        Cache::delete('plugins');

        return true;
    }

    /**
     * 禁用插件
     */
    public function disable($pluginName)
    {
        $pluginInfo = $this->getPluginInfo($pluginName);
        if (!$pluginInfo) {
            throw new \Exception("插件 {$pluginName} 不存在");
        }

        // 执行插件卸载方法
        if (method_exists($pluginInfo['instance'], 'uninstall')) {
            $pluginInfo['instance']->uninstall();
        }

        // 更新插件状态
        $this->setPluginStatus($pluginName, false);

        // 清除缓存
        Cache::delete('plugins');

        return true;
    }

    /**
     * 检查插件是否启用
     */
    protected function isEnabled($pluginName)
    {
        $statusFile = $this->pluginPath . 'plugin_status.php';
        $statusList = is_file($statusFile) ? include $statusFile : [];

        return !empty($statusList[$pluginName]);
    }

    /**
     * 设置插件状态
     */
    protected function setPluginStatus($pluginName, $status)
    {
        $statusFile = $this->pluginPath . 'plugin_status.php';
        $statusList = is_file($statusFile) ? include $statusFile : [];
        $statusList[$pluginName] = $status;

        file_put_contents($statusFile, "<?php\nreturn " . var_export($statusList, true) . ";");
    }
}

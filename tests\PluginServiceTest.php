<?php

namespace tests;

use PHPUnit\Framework\TestCase;
use app\service\PluginService;
use think\App;
use think\facade\Route;

class PluginServiceTest extends TestCase
{
    protected $app;
    protected $pluginService;
    
    protected function setUp(): void
    {
        $this->app = new App();
        $this->pluginService = new PluginService($this->app);
    }
    
    /**
     * 测试路由模式匹配
     */
    public function testMatchRoutePattern()
    {
        $reflection = new \ReflectionClass($this->pluginService);
        $method = $reflection->getMethod('matchRoutePattern');
        $method->setAccessible(true);
        
        // 测试通配符匹配
        $this->assertTrue($method->invoke($this->pluginService, 'demo/admin/users', 'demo/admin/*'));
        $this->assertTrue($method->invoke($this->pluginService, 'demo/admin/posts/edit', 'demo/admin/*'));
        $this->assertFalse($method->invoke($this->pluginService, 'demo/user/profile', 'demo/admin/*'));
        
        // 测试精确匹配
        $this->assertTrue($method->invoke($this->pluginService, 'demo/upload', 'demo/upload'));
        $this->assertFalse($method->invoke($this->pluginService, 'demo/upload/file', 'demo/upload'));
        
        // 测试复杂模式
        $this->assertTrue($method->invoke($this->pluginService, 'demo/user/123/edit', 'demo/*/edit'));
        $this->assertTrue($method->invoke($this->pluginService, 'demo/post/456/edit', 'demo/*/edit'));
        $this->assertFalse($method->invoke($this->pluginService, 'demo/user/123/view', 'demo/*/edit'));
    }
    
    /**
     * 测试路由配置注册
     */
    public function testRegisterConfigRoutes()
    {
        $routes = [
            [
                'method' => 'get',
                'rule' => 'test/index',
                'route' => 'test\controller\Index/index',
                'option' => [
                    'middleware' => ['test_auth'],
                    'cache' => 3600
                ]
            ],
            [
                'method' => 'post',
                'rule' => 'test/save',
                'route' => 'test\controller\Index/save',
                'option' => [
                    'ajax' => true,
                    'validate' => [
                        'name' => 'require'
                    ]
                ]
            ]
        ];
        
        $reflection = new \ReflectionClass($this->pluginService);
        $method = $reflection->getMethod('registerConfigRoutes');
        $method->setAccessible(true);
        
        // 这个测试主要验证方法不会抛出异常
        $this->assertNull($method->invoke($this->pluginService, $routes));
    }
    
    /**
     * 测试中间件别名注册
     */
    public function testRegisterMiddlewareAliases()
    {
        $aliases = [
            'test_auth' => 'test\middleware\Auth',
            'test_log' => 'test\middleware\Log'
        ];
        
        $reflection = new \ReflectionClass($this->pluginService);
        $method = $reflection->getMethod('registerMiddlewareAliases');
        $method->setAccessible(true);
        
        // 这个测试主要验证方法不会抛出异常
        $this->assertNull($method->invoke($this->pluginService, $aliases));
    }
    
    /**
     * 测试获取路由中间件
     */
    public function testGetRouteMiddlewares()
    {
        // 先存储一些路由中间件配置
        $routeMiddlewares = [
            'test/admin/*' => ['test_auth', 'test_log'],
            'test/api/*' => ['test_api_auth'],
            'test/upload' => ['test_upload']
        ];
        
        $reflection = new \ReflectionClass($this->pluginService);
        $storeMethod = $reflection->getMethod('storeRouteMiddlewares');
        $storeMethod->setAccessible(true);
        $storeMethod->invoke($this->pluginService, $routeMiddlewares, 'test');
        
        // 测试获取中间件
        $middlewares1 = $this->pluginService->getRouteMiddlewares('test/admin/users', 'test');
        $this->assertContains('test_auth', $middlewares1);
        $this->assertContains('test_log', $middlewares1);
        
        $middlewares2 = $this->pluginService->getRouteMiddlewares('test/api/data', 'test');
        $this->assertContains('test_api_auth', $middlewares2);
        
        $middlewares3 = $this->pluginService->getRouteMiddlewares('test/upload', 'test');
        $this->assertContains('test_upload', $middlewares3);
        
        $middlewares4 = $this->pluginService->getRouteMiddlewares('test/other', 'test');
        $this->assertEmpty($middlewares4);
    }
    
    /**
     * 测试路由选项应用
     */
    public function testApplyRouteOptions()
    {
        // 创建一个模拟的路由对象
        $routeObj = $this->createMock(\think\route\Rule::class);
        
        $options = [
            'middleware' => ['test_auth'],
            'cache' => 3600,
            'ajax' => true,
            'https' => true
        ];
        
        $reflection = new \ReflectionClass($this->pluginService);
        $method = $reflection->getMethod('applyRouteOptions');
        $method->setAccessible(true);
        
        // 这个测试主要验证方法不会抛出异常
        $this->assertNull($method->invoke($this->pluginService, $routeObj, $options));
    }
}

<?php

namespace plugins\demo\middleware;

use think\Request;
use think\Response;

/**
 * Demo 插件 API 认证中间件
 */
class ApiAuth
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next)
    {
        // API Key 认证
        $apiKey = $request->header('X-API-Key') ?: $request->param('api_key');
        
        if (!$apiKey) {
            return json([
                'error' => 'API Key required',
                'code' => 401,
                'message' => 'Please provide a valid API key'
            ], 401);
        }
        
        // 验证 API Key
        if (!$this->validateApiKey($apiKey)) {
            return json([
                'error' => 'Invalid API Key',
                'code' => 401,
                'message' => 'The provided API key is invalid or expired'
            ], 401);
        }
        
        // 检查API限制
        $apiInfo = $this->getApiInfo($apiKey);
        if (!$this->checkApiLimits($apiInfo, $request)) {
            return json([
                'error' => 'API Limit Exceeded',
                'code' => 429,
                'message' => 'API rate limit exceeded'
            ], 429);
        }
        
        // 将API信息添加到请求中
        $request->withAttribute('api_info', $apiInfo);
        
        return $next($request);
    }
    
    /**
     * 验证API Key
     */
    protected function validateApiKey($apiKey)
    {
        // 这里应该从数据库中验证API Key
        // 简单示例：检查格式和长度
        return preg_match('/^[a-zA-Z0-9]{32}$/', $apiKey);
    }
    
    /**
     * 获取API信息
     */
    protected function getApiInfo($apiKey)
    {
        // 这里应该从数据库获取API信息
        return [
            'api_key' => $apiKey,
            'user_id' => 1,
            'app_name' => 'Demo App',
            'rate_limit' => 1000, // 每小时1000次请求
            'permissions' => ['read', 'write']
        ];
    }
    
    /**
     * 检查API限制
     */
    protected function checkApiLimits($apiInfo, Request $request)
    {
        // 这里应该实现真正的限流逻辑
        // 可以使用Redis或其他缓存来记录请求次数
        return true;
    }
}

<?php
// demo 插件路由配置示例
return [
    // 基本路由配置
    [
        'method' => 'get',
        'rule' => 'demo/index',
        'route' => 'plugins\demo\controller\Index/index',
        'option' => [
            'middleware' => ['demo_auth'],
            'cache' => 3600
        ]
    ],
    
    // POST 路由配置
    [
        'method' => 'post',
        'rule' => 'demo/save',
        'route' => 'plugins\demo\controller\Index/save',
        'option' => [
            'middleware' => ['demo_auth', 'demo_log'],
            'ajax' => true,
            'validate' => [
                'name' => 'require|max:50',
                'email' => 'email'
            ]
        ]
    ],
    
    // 资源路由配置
    [
        'method' => 'resource',
        'rule' => 'demo/article',
        'route' => 'plugins\demo\controller\Article',
        'option' => [
            'middleware' => ['demo_auth'],
            'except' => ['delete']
        ]
    ],
    
    // 支持多种请求方法的路由
    [
        'method' => 'get|post',
        'rule' => 'demo/upload',
        'route' => 'plugins\demo\controller\Upload/index',
        'option' => [
            'middleware' => ['demo_auth', 'demo_upload'],
            'https' => true
        ]
    ],
    
    // 带参数的路由
    [
        'method' => 'get',
        'rule' => 'demo/user/:id',
        'route' => 'plugins\demo\controller\User/show',
        'option' => [
            'middleware' => ['demo_auth'],
            'pattern' => [
                'id' => '\d+'
            ]
        ]
    ],
    
    // API 路由组（通过前缀实现）
    [
        'method' => 'get',
        'rule' => 'demo/api/users',
        'route' => 'plugins\demo\controller\api\User/index',
        'option' => [
            'middleware' => ['demo_api_auth'],
            'json' => true,
            'allow_cross_domain' => true
        ]
    ],
    
    [
        'method' => 'post',
        'rule' => 'demo/api/users',
        'route' => 'plugins\demo\controller\api\User/create',
        'option' => [
            'middleware' => ['demo_api_auth'],
            'json' => true,
            'allow_cross_domain' => true,
            'validate' => [
                'name' => 'require|max:50',
                'email' => 'require|email|unique:users'
            ]
        ]
    ]
];

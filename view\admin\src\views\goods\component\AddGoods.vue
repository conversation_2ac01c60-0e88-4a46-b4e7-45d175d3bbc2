<script setup>
import { ref, computed, defineProps, defineEmits, reactive, watch } from 'vue'
import { Cascader } from 'ant-design-vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import SkuTable from './SkuTable.vue'
import Editor from '@/components/editor/Editor.vue'
import Upload from '@/components/editor/Upload.vue'
import Media from '@/components/editor/Media.vue'

const activeKey = ref('1')
const videoUploadVisible = ref(false)
const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: true,
  },
})
const fileList = ref([])
const options = [
  {
    label: 'Light',
    value: 'light',
    children: new Array(20).fill(null).map((_, index) => ({
      label: `Number ${index}`,
      value: index,
    })),
  },
  {
    label: 'Bamboo',
    value: 'bamboo',
    children: [
      {
        label: 'Little',
        value: 'little',
        children: [
          {
            label: 'Toy Fish',
            value: 'fish',
          },
          {
            label: 'Toy Cards',
            value: 'cards',
          },
          {
            label: 'Toy Bird',
            value: 'bird',
          },
        ],
      },
    ],
  },
]

const shippingOptions = ref([
  {
    value: 'jack',
    label: '国内运费',
  },
  {
    value: 'lucy',
    label: '偏远地区运费',
  },
])

const form = reactive({
  type: '1',
  goods_name: '',
  category_id: [],
  goods_image: [],
  goods_code: '',
  deliveryType: 0,
  shippingType: 1,
  shippingCost: 0,
  shippingTemplateId: null,
  status: 10,
  sort: 10,
  specs_type: 'single',
  goods_price: '0.00',
  goods_line_price: '0.00',
  goods_stock: 0,
  goods_weight: 0,
  spec_groups: [],
  sku_list: [],
  deduct_stock_type: '',
  purchase_limit: 0,
  content: '',
  services: [],
})

// 监听content变化，确保类型正确
watch(
  () => form.content,
  (newValue) => {
    if (typeof newValue !== 'string' && newValue !== null && newValue !== undefined) {
      form.content = String(newValue)
    }
  },
  { immediate: true },
)

// 处理SKU列表更新
const handleSkuListUpdated = (newSkuList) => {
  form.sku_list = newSkuList
}

// Upload组件相关
const uploadVisible = ref(false)

// 处理图片选择
const handleImageSelect = (selectedImages) => {
  // selectedImages 是选中的图片索引数组
  if (selectedImages && selectedImages.length > 0) {
    // 这里可以根据索引获取实际的图片数据
    // 模拟添加选中的图片到fileList
    selectedImages.forEach((index) => {
      const newImage = {
        uid: Date.now().toString() + index,
        name: `image_${Date.now()}_${index}.jpg`,
        status: 'done',
        url: `https://picsum.photos/200/200?random=${index + 1}`,
        thumbUrl: `https://picsum.photos/200/200?random=${index + 1}`,
      }
      if (fileList.value.length < 8) {
        fileList.value = [...fileList.value, newImage]
      }
    })
  }
  uploadVisible.value = false
}

// 删除图片
const removeImage = (index) => {
  const newList = [...fileList.value]
  newList.splice(index, 1)
  fileList.value = newList
}

const open = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  },
})
// 添加规格组
const addSpecGroup = () => {
  if (form.spec_groups.length >= 3) {
    return
  }
  const newId = Math.max(...form.spec_groups.map((g) => g.id), 0) + 1
  form.spec_groups.push({
    id: newId,
    name: '',
    specs: [{ id: Date.now(), name: '' }],
  })
}

// 删除规格组
const removeSpecGroup = (groupId) => {
  form.spec_groups = form.spec_groups.filter((group) => group.id !== groupId)
}

// 添加规格
const addSpec = (groupId) => {
  const group = form.spec_groups.find((g) => g.id === groupId)
  if (group && group.specs.length < 10) {
    group.specs.push({
      id: Date.now(),
      name: '',
    })
  }
}

// 删除规格
const removeSpec = (groupId, specId) => {
  const group = form.spec_groups.find((g) => g.id === groupId)
  if (group && group.specs.length > 1) {
    group.specs = group.specs.filter((spec) => spec.id !== specId)
  }
}

const videoList = ref([])
const handleVideoRemove = () => {}
const handleVideoSelect = () => {}

const handleClose = () => {
  open.value = false
}
const handleCancle = () => {
  open.value = false
}
</script>
<template>
  <div class="container">
    <a-drawer
      title="新增商品"
      v-model:open="open"
      placement="right"
      size="large"
      :footer-style="{ textAlign: 'right' }"
      @close="handleClose"
    >
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="基本信息">
          <a-form :model="form" layout="vertical">
            <a-form-item label="商品类型">
              <a-radio-group v-model:value="form.type" size="large" class="ant-radio-group-card">
                <a-radio-button value="1">实物商品<span>物流发货</span></a-radio-button>
                <a-radio-button value="2">虚拟商品<span>无需物流</span></a-radio-button>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="商品名称">
              <a-input />
            </a-form-item>
            <a-form-item label="商品分类">
              <a-cascader
                v-model:value="form.category_id"
                style="width: 290px"
                multiple
                max-tag-count="responsive"
                :options="options"
                placeholder="请选择分类"
                :show-checked-strategy="Cascader.SHOW_CHILD"
              ></a-cascader>
            </a-form-item>
            <a-form-item
              label="商品图片"
              extra="建议尺寸：750*750像素, 最多上传10张,第1张为商品首图"
            >
              <!-- Upload组件 -->
              <Upload
                v-model="uploadVisible"
                :show-button="true"
                :file-list="fileList"
                :max-count="8"
                @image-select="handleImageSelect"
                @remove-image="removeImage"
              />
            </a-form-item>
            <a-form-item label="商品编码">
              <a-input style="width: 290px" />
            </a-form-item>
            <a-form-item label="配送方式">
              <a-radio-group v-model:value="form.deliveryType">
                <a-radio value="1">快递配送</a-radio>
                <a-radio value="2">上门自提</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="快递运费">
              <a-radio-group v-model:value="form.shippingType" class="shipping-cost">
                <a-radio :value="1">
                  <span>固定运费</span>
                  <a-input prefix="￥" placeholder="请填写运费" v-model="form.shippingCost"
                /></a-radio>
                <a-radio :value="2">
                  <span>运费模板</span>
                  <a-select
                    v-model:value="form.shippingTemplateId"
                    placeholder="选择运费模板"
                    :options="shippingOptions"
                  ></a-select>
                </a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="是否上架">
              <a-radio-group v-model:value="form.status">
                <a-radio value="1">上架</a-radio>
                <a-radio value="2">下架</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="商品排序">
              <a-input style="width: 100px" v-model:value="form.sort" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="2" tab="规格库存">
          <a-form :model="form" layout="vertical">
            <a-form-item label="商品规格">
              <a-radio-group v-model:value="form.specs_type">
                <a-radio value="single">单规格</a-radio>
                <a-radio value="multiple">多规格</a-radio>
              </a-radio-group>
              <div v-if="form.specs_type === 'multiple'" class="specs">
                <div class="specs-content">
                  <div class="specs-tip">
                    商品规格：参与商品规格的属性，生成SKU时会根据规格属性进行组合
                  </div>
                  <div v-for="group in form.spec_groups" :key="group.id" class="spec-group">
                    <div class="spec-group-header">
                      <a-input
                        v-model:value="group.name"
                        placeholder="规格组名称，如：颜色、尺寸等"
                        style="width: 230px"
                      />
                      <a-button
                        size="small"
                        type="link"
                        @click="removeSpecGroup(group.id)"
                        :disabled="form.spec_groups.length <= 1"
                      >
                        删除
                      </a-button>
                    </div>
                    <div class="spec-items">
                      <div v-for="spec in group.specs" :key="spec.id" class="spec-item">
                        <a-input
                          v-model:value="spec.name"
                          placeholder="规格值"
                          style="width: 150px"
                        />
                        <a-button
                          size="small"
                          shape="circle"
                          @click="removeSpec(group.id, spec.id)"
                          :disabled="group.specs.length <= 1"
                        >
                          <CloseOutlined />
                        </a-button>
                      </div>
                      <a-button
                        type="link"
                        @click="addSpec(group.id)"
                        :disabled="group.specs.length >= 10"
                      >
                        新增规格
                      </a-button>
                    </div>
                  </div>
                  <a-button
                    @click="addSpecGroup"
                    :disabled="form.spec_groups.length >= 3"
                    style="margin-top: 12px"
                  >
                    添加规格组
                  </a-button>
                  <div class="extra">最多添加3个商品规格组，生成的SKU数量不能超出50个</div>
                </div>
              </div>
            </a-form-item>
            <a-form-item label="SKU属性" v-if="form.specs_type == 'multiple'">
              <SkuTable
                :specGroups="form.spec_groups"
                :specsType="form.specs_type"
                @update:skuList="handleSkuListUpdated"
              />
            </a-form-item>
            <template v-else>
              <a-form-item label="商品价格">
                <a-input
                  v-model:value="form.goods_price"
                  prefix="￥"
                  suffix="元"
                  style="max-width: 200px"
                />
              </a-form-item>
              <a-form-item label="划线价格">
                <a-input
                  v-model:value="form.goods_line_price"
                  prefix="￥"
                  suffix="元"
                  style="max-width: 200px"
                />
              </a-form-item>
              <a-form-item label="库存数量">
                <a-input v-model:value="form.goods_stock" suffix="件" style="max-width: 200px" />
              </a-form-item>
              <a-form-item label="商品重量">
                <a-input v-model:value="form.goods_weight" suffix="Kg" style="max-width: 200px" />
              </a-form-item>
            </template>
            <a-form-item label="库存减扣">
              <a-radio-group v-model:value="form.deduct_stock_type">
                <a-radio value="buy">拍下减库存</a-radio>
                <a-radio value="payment">付款减库存</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="商品限购" extra="用于限制每人购买该商品的数量">
              <a-radio-group v-model:value="form.purchase_limit">
                <a-radio :value="0">关闭</a-radio>
                <a-radio :value="1">开启</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="3" tab="商品详情">
          <a-form :model="form" layout="vertical">
            <a-form-item label="商品详情">
              <editor v-model="form.content" placeholder="商品详情描述"
            /></a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="4" tab="更多设置">
          <a-form :model="form" layout="vertical">
            <a-form-item label="商品简述">
              <a-input placeholder="一句话描述商品卖点" />
            </a-form-item>
            <a-form-item label="商品销量">
              <a-input placeholder="初始销量" style="width: 100px" />
            </a-form-item>
            <a-form-item label="主图视频">
              <Media
                v-model="videoUploadVisible"
                :show-button="true"
                size="default"
                :max-count="5"
                :file-list="videoList"
                @video-select="handleVideoSelect"
                @remove-video="handleVideoRemove"
              />
            </a-form-item>
            <a-form-item label="服务承诺">
              <a-checkbox-group v-model:value="form.services">
                <a-checkbox value="buy">7天无理由退换货 </a-checkbox>
                <a-checkbox value="payment">24小时发货 </a-checkbox>
              </a-checkbox-group>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
      <template #footer>
        <a-button style="margin-right: 8px" @click="handleCancle">取消</a-button>
        <a-button type="primary" @click="handleClose">确认</a-button>
      </template>
    </a-drawer>
  </div>
</template>
<style lang="less" scoped>
.ant-tabs {
  margin-top: -12px;
  margin-bottom: -12px;
}

.ant-radio-group {
  &.ant-radio-group-card {
    display: flex;
    gap: 12px;
    :deep(.ant-radio-button-wrapper) {
      display: flex;
      height: 60px;
      line-height: 22px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      position: relative;
      overflow: hidden;
      padding-inline: 20px;
      &::before {
        background: none;
      }

      // 重置inset属性
      &::after,
      &::before {
        inset-block-start: unset !important;
        inset-inline-start: unset !important;
        box-sizing: unset !important;
        padding-block: 0px;
        padding-inline: 0;
      }
      &.ant-radio-button-wrapper-checked {
        border-color: #1677ff;
        position: relative;
        overflow: hidden; // 隐藏超出部分
        &::after {
          content: ' ';
          position: absolute;
          right: -16px;
          bottom: -16px;
          width: 0;
          height: 0;
          border: 16px solid transparent;
          border-bottom-color: #1677ff;
          transform: rotate(135deg);
          z-index: 1;
        }
        &::before {
          content: ' ';
          position: absolute;
          right: 2px; // 调整位置
          bottom: 6px; // 调整位置
          width: 8px; // 对号宽度
          height: 4px; // 对号高度
          border: 2px solid #fff;
          border-top: none;
          border-right: none;
          transform: rotate(-45deg);
          z-index: 2;
        }
      }
      span {
        font-size: 14px;
        &:last-child {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          span {
            font-size: 14px;
            color: #999;
          }
        }
      }
    }
  }
}
//上传按钮大小
:deep(.ant-upload-picture-card-wrapper) {
  .ant-upload.ant-upload-select,
  .ant-upload-list-item-container {
    width: 80px !important;
    height: 80px !important;
  }
}
.shipping-cost {
  &.ant-radio-group {
    display: flex;
    gap: 12px;
    :deep(.ant-radio-wrapper) {
      span.ant-radio + * {
        display: flex;
        align-items: center;
        width: 230px;
        gap: 12px;
        .ant-input,
        .ant-select,
        .ant-input-affix-wrapper {
          flex: 1;
        }
      }
    }
  }
}
.specs {
  margin-top: 16px;

  .specs-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    font-weight: 500;
  }

  .specs-content {
    background-color: #f5f7fa;
    border-radius: 6px;
    padding: 16px;
  }

  .specs-tip {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    margin-bottom: 16px;
    padding: 8px 12px;
    background-color: #e6f7ff;
    border-left: 3px solid #1890ff;
    border-radius: 4px;
  }

  .spec-group {
    background-color: #ffffff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 12px;

    .spec-group-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 12px;
      margin-bottom: 12px;
      position: relative;
      border-bottom: 1px solid #ececec;
      .ant-btn {
        position: absolute;
        right: -8px;
        top: -8px;
      }
    }

    .spec-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      align-items: center;

      .spec-item {
        display: flex;
        align-items: center;
        gap: 8px;
        position: relative;
        .ant-btn {
          color: #fff;
          position: absolute;
          right: -4px;
          top: -8px;
          background-color: #000;
          min-width: 18px;
          font-size: 8px;
          height: 18px;
          padding: 0px;
          &:hover {
            border: none;
          }
        }
      }
    }
  }

  .extra {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    margin-top: 12px;
  }
}
</style>

<?php

declare(strict_types=1);

namespace app\admin\middleware;

use app\common\third\Token;

class Auth
{
    /**
     * 验证TOKEN
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        $token = $request->header('Authorization');
        if (!$token || !Token::check($token)) {
            return json(['error' => 'Unauthorized'], 401);
        }
        return $next($request);
    }
}

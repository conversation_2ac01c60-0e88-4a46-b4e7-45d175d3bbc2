<?php

namespace plugins\demo\middleware;

use think\Request;
use think\Response;

/**
 * Demo 插件认证中间件
 */
class Auth
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next)
    {
        // 检查用户是否已登录
        $token = $request->header('Authorization') ?: $request->param('token');
        
        if (!$token) {
            return json(['error' => 'Token required', 'code' => 401], 401);
        }
        
        // 这里可以添加更复杂的token验证逻辑
        if (!$this->validateToken($token)) {
            return json(['error' => 'Invalid token', 'code' => 401], 401);
        }
        
        // 将用户信息添加到请求中
        $userInfo = $this->getUserByToken($token);
        $request->withAttribute('user', $userInfo);
        
        return $next($request);
    }
    
    /**
     * 验证token
     */
    protected function validateToken($token)
    {
        // 简单的token验证示例
        // 实际项目中应该使用更安全的验证方式
        return !empty($token) && strlen($token) > 10;
    }
    
    /**
     * 根据token获取用户信息
     */
    protected function getUserByToken($token)
    {
        // 这里应该从数据库或缓存中获取用户信息
        return [
            'id' => 1,
            'name' => 'Demo User',
            'email' => '<EMAIL>',
            'token' => $token
        ];
    }
}

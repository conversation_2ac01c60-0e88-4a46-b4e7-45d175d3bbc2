<?php
namespace plugins\demo;

use plugins\Plugin as BasePlugin;

class Plugin extends BasePlugin
{
    /**
     * 插件信息
     */
    public function info()
    {
        return [
            'name'        => 'demo',
            'title'       => 'demo Plugin',
            'description' => 'A Xshop plugin',
            'author'      => 'Your Name',
            'version'     => '1.0.0',
            'created_at' => '2025-08-25 16:56:15',
            'updated_at' => '2025-08-25 16:56:15'
        ];
    }

    /**
     * 安装方法
     */
    public function install()
    {
        // 执行安装操作
        // 例如：创建数据库表、初始化数据等
        return true;
    }

    /**
     * 卸载方法
     */
    public function uninstall()
    {
        // 执行卸载操作
        // 注意：谨慎处理数据删除
        return true;
    }

    /**
     * 启用插件
     */
    public function enable()
    {
        // 插件启用时的逻辑
        return true;
    }

    /**
     * 禁用插件
     */
    public function disable()
    {
        // 插件禁用时的逻辑
        return true;
    }
}
<?php

namespace plugins\demo\middleware;

use think\Request;
use think\Response;

/**
 * Demo 插件 CORS 中间件
 */
class Cors
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next)
    {
        // 处理预检请求
        if ($request->method() === 'OPTIONS') {
            return $this->handlePreflightRequest($request);
        }
        
        // 处理实际请求
        $response = $next($request);
        
        // 添加CORS头
        return $this->addCorsHeaders($response, $request);
    }
    
    /**
     * 处理预检请求
     */
    protected function handlePreflightRequest(Request $request)
    {
        $response = response('', 200);
        return $this->addCorsHeaders($response, $request);
    }
    
    /**
     * 添加CORS头
     */
    protected function addCorsHeaders(Response $response, Request $request)
    {
        $origin = $request->header('Origin');
        
        // 允许的域名列表
        $allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:8080',
            'https://demo.example.com'
        ];
        
        // 检查是否允许该域名
        if (in_array($origin, $allowedOrigins) || $this->isAllowedOrigin($origin)) {
            $response->header('Access-Control-Allow-Origin', $origin);
        }
        
        $response->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $response->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, X-Requested-With');
        $response->header('Access-Control-Allow-Credentials', 'true');
        $response->header('Access-Control-Max-Age', '86400'); // 24小时
        
        return $response;
    }
    
    /**
     * 检查是否允许该域名
     */
    protected function isAllowedOrigin($origin)
    {
        // 可以在这里实现更复杂的域名验证逻辑
        // 例如：通配符匹配、正则表达式等
        
        // 允许本地开发环境
        if (preg_match('/^https?:\/\/localhost(:\d+)?$/', $origin)) {
            return true;
        }
        
        // 允许特定子域名
        if (preg_match('/^https?:\/\/[\w-]+\.example\.com$/', $origin)) {
            return true;
        }
        
        return false;
    }
}

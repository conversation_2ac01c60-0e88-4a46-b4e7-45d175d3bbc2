<?php

declare(strict_types=1);

namespace app\admin\controller;

use think\exception\ValidateException;
use think\facade\Filesystem;
use think\Request;

class Upload
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function image()
    {
        $file = request()->file('image');
        try {
            $savename = Filesystem::putFile('topic', $file);
        } catch (ValidateException $e) {
            echo $e->getMessage();
        }
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function file()
    {
        $files = request()->file();
        try {
            validate(['image' => 'fileSize:10240|fileExt:jpg|image:200,200,jpg'])
                ->check($files);
            $savename = [];
            foreach ($files as $file) {
                $savename[] = Filesystem::putFile('topic', $file);
            }
        } catch (ValidateException $e) {
            echo $e->getMessage();
        }
    }

    /**
     * 保存新建的资源
     *
     * @param  \think\Request  $request
     * @return \think\Response
     */
    public function save(Request $request)
    {
        //
    }

    /**
     * 显示指定的资源
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function read($id)
    {
        //
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * 保存更新的资源
     *
     * @param  \think\Request  $request
     * @param  int  $id
     * @return \think\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * 删除指定资源
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function delete($id)
    {
        //
    }
}

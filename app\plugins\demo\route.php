<?php
use think\facade\Route;

// demo 插件路由定义
Route::group('demo', function () {
    // 插件首页
    Route::get('index', 'plugins\demo\controller\Index/index');
    
    // 配置页面
    Route::get('config', 'plugins\demo\controller\Index/config');
    
    // API 接口示例
    Route::group('api', function () {
        Route::get('data', 'plugins\demo\controller\Index/getData');
        Route::post('save', 'plugins\demo\controller\Index/saveData');
    });
})->allowCrossDomain();
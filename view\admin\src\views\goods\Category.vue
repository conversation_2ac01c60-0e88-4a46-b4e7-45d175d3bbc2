<script setup>
import { reactive, ref } from 'vue'
import Upload from '@/components/editor/Upload.vue'
const isCreate = ref(false)
const isUpload = ref(false)
const fileList = ref([])
const form = reactive({
  name: '',
  pid: 0,
  image: 0,
  status: 1,
  sort: 0,
})
const columns = [
  {
    title: '分类ID',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '分类图片',
    dataIndex: 'image',
    key: 'image',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
]
const dataList = ref([])

const onSubmit = () => {}
const onCancel = () => {}
const handleImageSelect = () => {}
const removeImage = () => {}
</script>
<template>
  <div class="container">
    <div class="toolbar">
      <a-button type="primary" @click="isCreate = true">新增</a-button>
    </div>
    <a-table :dataSource="dataList" :columns="columns"> </a-table>
  </div>
  <a-drawer title="新增分类" v-model:open="isCreate" :footer-style="{ textAlign: 'right' }">
    <a-form :model="form" layout="vertical">
      <a-form-item label="分类名称"><a-input v-model:value="form.name" /></a-form-item>
      <a-form-item label="上级分类"
        ><a-tree-select v-model:value="form.pid"></a-tree-select
      ></a-form-item>
      <a-form-item label="分类图片"
        ><Upload
          v-model="isUpload"
          :show-button="true"
          :file-list="fileList"
          :max-count="1"
          @image-select="handleImageSelect"
          @remove-image="removeImage"
        />
      </a-form-item>
      <a-form-item label="是否显示"
        ><a-radio-group v-model:value="form.status">
          <a-radio :value="1">显示</a-radio>
          <a-radio :value="2">隐藏</a-radio>
        </a-radio-group></a-form-item
      >
      <a-form-item label="排序" extra="数字越小越靠前"
        ><a-input-number v-model:value="form.sort"
      /></a-form-item>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onCancel">取消</a-button>
        <a-button type="primary" @click="onSubmit">确定</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<style lang="less" scoped>
.container {
  width: 100%;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  background-color: #fff;
}
</style>
